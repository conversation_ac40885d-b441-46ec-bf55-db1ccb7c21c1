# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import pydantic
import typing
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class RealtimeVoiceSettings(UncheckedBaseModel):
    stability: float = pydantic.Field()
    """
    Defines the stability for voice settings.
    """

    similarity_boost: float = pydantic.Field()
    """
    Defines the similarity boost for voice settings.
    """

    style: typing.Optional[float] = pydantic.Field(default=None)
    """
    Defines the style for voice settings. This parameter is available on V2+ models.
    """

    use_speaker_boost: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Defines the use speaker boost for voice settings. This parameter is available on V2+ models.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
