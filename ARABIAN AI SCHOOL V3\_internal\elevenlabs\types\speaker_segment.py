# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from .dubbed_segment import DubbedSegment
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic


class SpeakerSegment(UncheckedBaseModel):
    id: str
    start_time: float
    end_time: float
    text: str
    dubs: typing.Dict[str, DubbedSegment]

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
