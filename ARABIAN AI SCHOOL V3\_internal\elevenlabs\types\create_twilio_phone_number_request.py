# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import pydantic
import typing
from .telephony_provider import TelephonyProvider
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class CreateTwilioPhoneNumberRequest(UncheckedBaseModel):
    phone_number: str = pydantic.Field()
    """
    Phone number
    """

    provider: typing.Optional[TelephonyProvider] = None
    label: str = pydantic.Field()
    """
    Label for the phone number
    """

    sid: str = pydantic.Field()
    """
    Twilio Account SID
    """

    token: str = pydantic.Field()
    """
    Twilio Auth Token
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
