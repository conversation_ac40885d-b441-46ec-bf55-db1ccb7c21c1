# This file was auto-generated by <PERSON>rn from our API Definition.

import typing
from ..core.client_wrapper import SyncClient<PERSON>rapper
from ..core.request_options import RequestOptions
from ..types.dubbing_resource import DubbingResource
from ..core.jsonable_encoder import jsonable_encoder
from ..core.unchecked_base_model import construct_type
from ..errors.unprocessable_entity_error import UnprocessableEntityError
from ..types.http_validation_error import HttpValidationError
from json.decoder import J<PERSON>NDecodeError
from ..core.api_error import ApiError
from ..types.language_added_response import LanguageAddedResponse
from ..types.segment_create_response import SegmentCreateResponse
from ..types.segment_update_response import SegmentUpdateResponse
from ..types.segment_delete_response import SegmentDeleteResponse
from ..types.segment_transcription_response import SegmentTranscriptionResponse
from ..types.segment_translation_response import SegmentTranslationResponse
from ..types.segment_dub_response import SegmentDubResponse
from ..types.do_dubbing_response import DoDubbingResponse
from ..types.dubbing_metadata_response import DubbingMetadataResponse
from ..types.delete_dubbing_response_model import DeleteDubbingResponseModel
from ..errors.forbidden_error import ForbiddenError
from ..errors.not_found_error import NotFoundError
from ..errors.too_early_error import TooEarlyError
from .types.dubbing_get_transcript_for_dub_request_format_type import (
    DubbingGetTranscriptForDubRequestFormatType,
)
from ..core.client_wrapper import AsyncClientWrapper

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class DubbingClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def get_dubbing_resource(
        self,
        dubbing_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> DubbingResource:
        """
        Given a dubbing ID generated from the '/v1/dubbing' endpoint with studio enabled, returns the dubbing resource.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DubbingResource
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.get_dubbing_resource(
            dubbing_id="dubbing_id",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/dubbing/resource/{jsonable_encoder(dubbing_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    DubbingResource,
                    construct_type(
                        type_=DubbingResource,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def add_language_to_resource(
        self,
        dubbing_id: str,
        *,
        language: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> LanguageAddedResponse:
        """
        Adds the given ElevenLab Turbo V2/V2.5 language code to the resource. Does not automatically generate transcripts/translations/audio.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        language : typing.Optional[str]
            The Target language.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        LanguageAddedResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.add_language_to_resource(
            dubbing_id="dubbing_id",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/dubbing/resource/{jsonable_encoder(dubbing_id)}/language",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "language": language,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    LanguageAddedResponse,
                    construct_type(
                        type_=LanguageAddedResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def create_segment_for_speaker(
        self,
        dubbing_id: str,
        speaker_id: str,
        *,
        start_time: float,
        end_time: float,
        text: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SegmentCreateResponse:
        """
        Creates a new segment in dubbing resource with a start and end time for the speaker in every available language. Does not automatically generate transcripts/translations/audio.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        speaker_id : str
            ID of the speaker.

        start_time : float

        end_time : float

        text : typing.Optional[str]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SegmentCreateResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.create_segment_for_speaker(
            dubbing_id="dubbing_id",
            speaker_id="speaker_id",
            start_time=1.1,
            end_time=1.1,
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/dubbing/resource/{jsonable_encoder(dubbing_id)}/speaker/{jsonable_encoder(speaker_id)}/segment",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "start_time": start_time,
                "end_time": end_time,
                "text": text,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    SegmentCreateResponse,
                    construct_type(
                        type_=SegmentCreateResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def update_segment_language(
        self,
        dubbing_id: str,
        segment_id: str,
        language: str,
        *,
        start_time: typing.Optional[float] = OMIT,
        end_time: typing.Optional[float] = OMIT,
        text: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SegmentUpdateResponse:
        """
        Modifies a single segment with new text and/or start/end times. Will update the values for only a specific language of a segment. Does not automatically regenerate the dub.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        segment_id : str
            ID of the segment

        language : str
            ID of the language.

        start_time : typing.Optional[float]

        end_time : typing.Optional[float]

        text : typing.Optional[str]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SegmentUpdateResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.update_segment_language(
            dubbing_id="dubbing_id",
            segment_id="segment_id",
            language="language",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/dubbing/resource/{jsonable_encoder(dubbing_id)}/segment/{jsonable_encoder(segment_id)}/{jsonable_encoder(language)}",
            base_url=self._client_wrapper.get_environment().base,
            method="PATCH",
            json={
                "start_time": start_time,
                "end_time": end_time,
                "text": text,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    SegmentUpdateResponse,
                    construct_type(
                        type_=SegmentUpdateResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def delete_segment(
        self,
        dubbing_id: str,
        segment_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SegmentDeleteResponse:
        """
        Deletes a single segment from the dubbing.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        segment_id : str
            ID of the segment

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SegmentDeleteResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.delete_segment(
            dubbing_id="dubbing_id",
            segment_id="segment_id",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/dubbing/resource/{jsonable_encoder(dubbing_id)}/segment/{jsonable_encoder(segment_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    SegmentDeleteResponse,
                    construct_type(
                        type_=SegmentDeleteResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def transcribe_segments(
        self,
        dubbing_id: str,
        *,
        segments: typing.Sequence[str],
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SegmentTranscriptionResponse:
        """
        Regenerate the transcriptions for the specified segments. Does not automatically regenerate translations or dubs.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        segments : typing.Sequence[str]
            Transcribe this specific list of segments.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SegmentTranscriptionResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.transcribe_segments(
            dubbing_id="dubbing_id",
            segments=["segments"],
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/dubbing/resource/{jsonable_encoder(dubbing_id)}/transcribe",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "segments": segments,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    SegmentTranscriptionResponse,
                    construct_type(
                        type_=SegmentTranscriptionResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def translate_segments(
        self,
        dubbing_id: str,
        *,
        segments: typing.Sequence[str],
        languages: typing.Optional[typing.Sequence[str]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SegmentTranslationResponse:
        """
        Regenerate the translations for either the entire resource or the specified segments/languages. Will automatically transcribe missing transcriptions. Will not automatically regenerate the dubs.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        segments : typing.Sequence[str]
            Translate only this list of segments.

        languages : typing.Optional[typing.Sequence[str]]
            Translate only these languages for each segment.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SegmentTranslationResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.translate_segments(
            dubbing_id="dubbing_id",
            segments=["segments"],
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/dubbing/resource/{jsonable_encoder(dubbing_id)}/translate",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "segments": segments,
                "languages": languages,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    SegmentTranslationResponse,
                    construct_type(
                        type_=SegmentTranslationResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def dub_segments(
        self,
        dubbing_id: str,
        *,
        segments: typing.Sequence[str],
        languages: typing.Optional[typing.Sequence[str]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SegmentDubResponse:
        """
        Regenerate the dubs for either the entire resource or the specified segments/languages. Will automatically transcribe and translate any missing transcriptions and translations.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        segments : typing.Sequence[str]
            Dub only this list of segments.

        languages : typing.Optional[typing.Sequence[str]]
            Dub only these languages for each segment.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SegmentDubResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.dub_segments(
            dubbing_id="dubbing_id",
            segments=["segments"],
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/dubbing/resource/{jsonable_encoder(dubbing_id)}/dub",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "segments": segments,
                "languages": languages,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    SegmentDubResponse,
                    construct_type(
                        type_=SegmentDubResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def dub_a_video_or_an_audio_file(
        self,
        *,
        file: typing.Optional[str] = OMIT,
        csv_file: typing.Optional[str] = OMIT,
        foreground_audio_file: typing.Optional[str] = OMIT,
        background_audio_file: typing.Optional[str] = OMIT,
        name: typing.Optional[str] = OMIT,
        source_url: typing.Optional[str] = OMIT,
        source_lang: typing.Optional[str] = OMIT,
        target_lang: typing.Optional[str] = OMIT,
        num_speakers: typing.Optional[int] = OMIT,
        watermark: typing.Optional[bool] = OMIT,
        start_time: typing.Optional[int] = OMIT,
        end_time: typing.Optional[int] = OMIT,
        highest_resolution: typing.Optional[bool] = OMIT,
        drop_background_audio: typing.Optional[bool] = OMIT,
        use_profanity_filter: typing.Optional[bool] = OMIT,
        dubbing_studio: typing.Optional[bool] = OMIT,
        disable_voice_cloning: typing.Optional[bool] = OMIT,
        mode: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> DoDubbingResponse:
        """
        Dubs a provided audio or video file into given language.

        Parameters
        ----------
        file : typing.Optional[str]
            A list of file paths to audio recordings intended for voice cloning

        csv_file : typing.Optional[str]
            CSV file containing transcription/translation metadata

        foreground_audio_file : typing.Optional[str]
            For use only with csv input

        background_audio_file : typing.Optional[str]
            For use only with csv input

        name : typing.Optional[str]
            Name of the dubbing project.

        source_url : typing.Optional[str]
            URL of the source video/audio file.

        source_lang : typing.Optional[str]
            Source language.

        target_lang : typing.Optional[str]
            The Target language to dub the content into.

        num_speakers : typing.Optional[int]
            Number of speakers to use for the dubbing. Set to 0 to automatically detect the number of speakers

        watermark : typing.Optional[bool]
            Whether to apply watermark to the output video.

        start_time : typing.Optional[int]
            Start time of the source video/audio file.

        end_time : typing.Optional[int]
            End time of the source video/audio file.

        highest_resolution : typing.Optional[bool]
            Whether to use the highest resolution available.

        drop_background_audio : typing.Optional[bool]
            An advanced setting. Whether to drop background audio from the final dub. This can improve dub quality where it's known that audio shouldn't have a background track such as for speeches or monologues.

        use_profanity_filter : typing.Optional[bool]
            [BETA] Whether transcripts should have profanities censored with the words '[censored]'

        dubbing_studio : typing.Optional[bool]
            Whether to prepare dub for edits in dubbing studio or edits as a dubbing resource.

        disable_voice_cloning : typing.Optional[bool]
            [BETA] Instead of using a voice clone in dubbing, use a similar voice from the ElevenLabs Voice Library.

        mode : typing.Optional[str]
            automatic or manual. Manual mode is only supported when creating a dubbing studio project

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DoDubbingResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.dub_a_video_or_an_audio_file()
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/dubbing",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            data={
                "file": file,
                "csv_file": csv_file,
                "foreground_audio_file": foreground_audio_file,
                "background_audio_file": background_audio_file,
                "name": name,
                "source_url": source_url,
                "source_lang": source_lang,
                "target_lang": target_lang,
                "num_speakers": num_speakers,
                "watermark": watermark,
                "start_time": start_time,
                "end_time": end_time,
                "highest_resolution": highest_resolution,
                "drop_background_audio": drop_background_audio,
                "use_profanity_filter": use_profanity_filter,
                "dubbing_studio": dubbing_studio,
                "disable_voice_cloning": disable_voice_cloning,
                "mode": mode,
            },
            files={},
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    DoDubbingResponse,
                    construct_type(
                        type_=DoDubbingResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_dubbing_project_metadata(
        self,
        dubbing_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> DubbingMetadataResponse:
        """
        Returns metadata about a dubbing project, including whether it's still in progress or not

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DubbingMetadataResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.get_dubbing_project_metadata(
            dubbing_id="dubbing_id",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/dubbing/{jsonable_encoder(dubbing_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    DubbingMetadataResponse,
                    construct_type(
                        type_=DubbingMetadataResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def delete_dubbing_project(
        self,
        dubbing_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> DeleteDubbingResponseModel:
        """
        Deletes a dubbing project.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteDubbingResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.delete_dubbing_project(
            dubbing_id="dubbing_id",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/dubbing/{jsonable_encoder(dubbing_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    DeleteDubbingResponseModel,
                    construct_type(
                        type_=DeleteDubbingResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_dubbed_file(
        self,
        dubbing_id: str,
        language_code: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Iterator[bytes]:
        """
        Returns dubbed file as a streamed file. Videos will be returned in MP4 format and audio only dubs will be returned in MP3.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        language_code : str
            ID of the language.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Yields
        ------
        typing.Iterator[bytes]
            The dubbed audio or video file
        """
        with self._client_wrapper.httpx_client.stream(
            f"v1/dubbing/{jsonable_encoder(dubbing_id)}/audio/{jsonable_encoder(language_code)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        ) as _response:
            try:
                if 200 <= _response.status_code < 300:
                    _chunk_size = request_options.get("chunk_size", 1024) if request_options is not None else 1024
                    for _chunk in _response.iter_bytes(chunk_size=_chunk_size):
                        yield _chunk
                    return
                _response.read()
                if _response.status_code == 403:
                    raise ForbiddenError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 404:
                    raise NotFoundError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 422:
                    raise UnprocessableEntityError(
                        typing.cast(
                            HttpValidationError,
                            construct_type(
                                type_=HttpValidationError,  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 425:
                    raise TooEarlyError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                _response_json = _response.json()
            except JSONDecodeError:
                raise ApiError(status_code=_response.status_code, body=_response.text)
            raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_transcript_for_dub(
        self,
        dubbing_id: str,
        language_code: str,
        *,
        format_type: typing.Optional[DubbingGetTranscriptForDubRequestFormatType] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> str:
        """
        Returns transcript for the dub as an SRT or WEBVTT file.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        language_code : str
            ID of the language.

        format_type : typing.Optional[DubbingGetTranscriptForDubRequestFormatType]
            Format to use for the subtitle file, either 'srt' or 'webvtt'

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        str
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.get_transcript_for_dub(
            dubbing_id="dubbing_id",
            language_code="language_code",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/dubbing/{jsonable_encoder(dubbing_id)}/transcript/{jsonable_encoder(language_code)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            params={
                "format_type": format_type,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    str,
                    construct_type(
                        type_=str,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 403:
                raise ForbiddenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 404:
                raise NotFoundError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 425:
                raise TooEarlyError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)


class AsyncDubbingClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def get_dubbing_resource(
        self,
        dubbing_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> DubbingResource:
        """
        Given a dubbing ID generated from the '/v1/dubbing' endpoint with studio enabled, returns the dubbing resource.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DubbingResource
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.get_dubbing_resource(
                dubbing_id="dubbing_id",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/dubbing/resource/{jsonable_encoder(dubbing_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    DubbingResource,
                    construct_type(
                        type_=DubbingResource,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def add_language_to_resource(
        self,
        dubbing_id: str,
        *,
        language: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> LanguageAddedResponse:
        """
        Adds the given ElevenLab Turbo V2/V2.5 language code to the resource. Does not automatically generate transcripts/translations/audio.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        language : typing.Optional[str]
            The Target language.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        LanguageAddedResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.add_language_to_resource(
                dubbing_id="dubbing_id",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/dubbing/resource/{jsonable_encoder(dubbing_id)}/language",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "language": language,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    LanguageAddedResponse,
                    construct_type(
                        type_=LanguageAddedResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def create_segment_for_speaker(
        self,
        dubbing_id: str,
        speaker_id: str,
        *,
        start_time: float,
        end_time: float,
        text: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SegmentCreateResponse:
        """
        Creates a new segment in dubbing resource with a start and end time for the speaker in every available language. Does not automatically generate transcripts/translations/audio.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        speaker_id : str
            ID of the speaker.

        start_time : float

        end_time : float

        text : typing.Optional[str]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SegmentCreateResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.create_segment_for_speaker(
                dubbing_id="dubbing_id",
                speaker_id="speaker_id",
                start_time=1.1,
                end_time=1.1,
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/dubbing/resource/{jsonable_encoder(dubbing_id)}/speaker/{jsonable_encoder(speaker_id)}/segment",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "start_time": start_time,
                "end_time": end_time,
                "text": text,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    SegmentCreateResponse,
                    construct_type(
                        type_=SegmentCreateResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def update_segment_language(
        self,
        dubbing_id: str,
        segment_id: str,
        language: str,
        *,
        start_time: typing.Optional[float] = OMIT,
        end_time: typing.Optional[float] = OMIT,
        text: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SegmentUpdateResponse:
        """
        Modifies a single segment with new text and/or start/end times. Will update the values for only a specific language of a segment. Does not automatically regenerate the dub.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        segment_id : str
            ID of the segment

        language : str
            ID of the language.

        start_time : typing.Optional[float]

        end_time : typing.Optional[float]

        text : typing.Optional[str]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SegmentUpdateResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.update_segment_language(
                dubbing_id="dubbing_id",
                segment_id="segment_id",
                language="language",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/dubbing/resource/{jsonable_encoder(dubbing_id)}/segment/{jsonable_encoder(segment_id)}/{jsonable_encoder(language)}",
            base_url=self._client_wrapper.get_environment().base,
            method="PATCH",
            json={
                "start_time": start_time,
                "end_time": end_time,
                "text": text,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    SegmentUpdateResponse,
                    construct_type(
                        type_=SegmentUpdateResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def delete_segment(
        self,
        dubbing_id: str,
        segment_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SegmentDeleteResponse:
        """
        Deletes a single segment from the dubbing.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        segment_id : str
            ID of the segment

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SegmentDeleteResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.delete_segment(
                dubbing_id="dubbing_id",
                segment_id="segment_id",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/dubbing/resource/{jsonable_encoder(dubbing_id)}/segment/{jsonable_encoder(segment_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    SegmentDeleteResponse,
                    construct_type(
                        type_=SegmentDeleteResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def transcribe_segments(
        self,
        dubbing_id: str,
        *,
        segments: typing.Sequence[str],
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SegmentTranscriptionResponse:
        """
        Regenerate the transcriptions for the specified segments. Does not automatically regenerate translations or dubs.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        segments : typing.Sequence[str]
            Transcribe this specific list of segments.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SegmentTranscriptionResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.transcribe_segments(
                dubbing_id="dubbing_id",
                segments=["segments"],
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/dubbing/resource/{jsonable_encoder(dubbing_id)}/transcribe",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "segments": segments,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    SegmentTranscriptionResponse,
                    construct_type(
                        type_=SegmentTranscriptionResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def translate_segments(
        self,
        dubbing_id: str,
        *,
        segments: typing.Sequence[str],
        languages: typing.Optional[typing.Sequence[str]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SegmentTranslationResponse:
        """
        Regenerate the translations for either the entire resource or the specified segments/languages. Will automatically transcribe missing transcriptions. Will not automatically regenerate the dubs.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        segments : typing.Sequence[str]
            Translate only this list of segments.

        languages : typing.Optional[typing.Sequence[str]]
            Translate only these languages for each segment.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SegmentTranslationResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.translate_segments(
                dubbing_id="dubbing_id",
                segments=["segments"],
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/dubbing/resource/{jsonable_encoder(dubbing_id)}/translate",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "segments": segments,
                "languages": languages,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    SegmentTranslationResponse,
                    construct_type(
                        type_=SegmentTranslationResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def dub_segments(
        self,
        dubbing_id: str,
        *,
        segments: typing.Sequence[str],
        languages: typing.Optional[typing.Sequence[str]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SegmentDubResponse:
        """
        Regenerate the dubs for either the entire resource or the specified segments/languages. Will automatically transcribe and translate any missing transcriptions and translations.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        segments : typing.Sequence[str]
            Dub only this list of segments.

        languages : typing.Optional[typing.Sequence[str]]
            Dub only these languages for each segment.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SegmentDubResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.dub_segments(
                dubbing_id="dubbing_id",
                segments=["segments"],
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/dubbing/resource/{jsonable_encoder(dubbing_id)}/dub",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "segments": segments,
                "languages": languages,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    SegmentDubResponse,
                    construct_type(
                        type_=SegmentDubResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def dub_a_video_or_an_audio_file(
        self,
        *,
        file: typing.Optional[str] = OMIT,
        csv_file: typing.Optional[str] = OMIT,
        foreground_audio_file: typing.Optional[str] = OMIT,
        background_audio_file: typing.Optional[str] = OMIT,
        name: typing.Optional[str] = OMIT,
        source_url: typing.Optional[str] = OMIT,
        source_lang: typing.Optional[str] = OMIT,
        target_lang: typing.Optional[str] = OMIT,
        num_speakers: typing.Optional[int] = OMIT,
        watermark: typing.Optional[bool] = OMIT,
        start_time: typing.Optional[int] = OMIT,
        end_time: typing.Optional[int] = OMIT,
        highest_resolution: typing.Optional[bool] = OMIT,
        drop_background_audio: typing.Optional[bool] = OMIT,
        use_profanity_filter: typing.Optional[bool] = OMIT,
        dubbing_studio: typing.Optional[bool] = OMIT,
        disable_voice_cloning: typing.Optional[bool] = OMIT,
        mode: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> DoDubbingResponse:
        """
        Dubs a provided audio or video file into given language.

        Parameters
        ----------
        file : typing.Optional[str]
            A list of file paths to audio recordings intended for voice cloning

        csv_file : typing.Optional[str]
            CSV file containing transcription/translation metadata

        foreground_audio_file : typing.Optional[str]
            For use only with csv input

        background_audio_file : typing.Optional[str]
            For use only with csv input

        name : typing.Optional[str]
            Name of the dubbing project.

        source_url : typing.Optional[str]
            URL of the source video/audio file.

        source_lang : typing.Optional[str]
            Source language.

        target_lang : typing.Optional[str]
            The Target language to dub the content into.

        num_speakers : typing.Optional[int]
            Number of speakers to use for the dubbing. Set to 0 to automatically detect the number of speakers

        watermark : typing.Optional[bool]
            Whether to apply watermark to the output video.

        start_time : typing.Optional[int]
            Start time of the source video/audio file.

        end_time : typing.Optional[int]
            End time of the source video/audio file.

        highest_resolution : typing.Optional[bool]
            Whether to use the highest resolution available.

        drop_background_audio : typing.Optional[bool]
            An advanced setting. Whether to drop background audio from the final dub. This can improve dub quality where it's known that audio shouldn't have a background track such as for speeches or monologues.

        use_profanity_filter : typing.Optional[bool]
            [BETA] Whether transcripts should have profanities censored with the words '[censored]'

        dubbing_studio : typing.Optional[bool]
            Whether to prepare dub for edits in dubbing studio or edits as a dubbing resource.

        disable_voice_cloning : typing.Optional[bool]
            [BETA] Instead of using a voice clone in dubbing, use a similar voice from the ElevenLabs Voice Library.

        mode : typing.Optional[str]
            automatic or manual. Manual mode is only supported when creating a dubbing studio project

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DoDubbingResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.dub_a_video_or_an_audio_file()


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/dubbing",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            data={
                "file": file,
                "csv_file": csv_file,
                "foreground_audio_file": foreground_audio_file,
                "background_audio_file": background_audio_file,
                "name": name,
                "source_url": source_url,
                "source_lang": source_lang,
                "target_lang": target_lang,
                "num_speakers": num_speakers,
                "watermark": watermark,
                "start_time": start_time,
                "end_time": end_time,
                "highest_resolution": highest_resolution,
                "drop_background_audio": drop_background_audio,
                "use_profanity_filter": use_profanity_filter,
                "dubbing_studio": dubbing_studio,
                "disable_voice_cloning": disable_voice_cloning,
                "mode": mode,
            },
            files={},
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    DoDubbingResponse,
                    construct_type(
                        type_=DoDubbingResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_dubbing_project_metadata(
        self,
        dubbing_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> DubbingMetadataResponse:
        """
        Returns metadata about a dubbing project, including whether it's still in progress or not

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DubbingMetadataResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.get_dubbing_project_metadata(
                dubbing_id="dubbing_id",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/dubbing/{jsonable_encoder(dubbing_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    DubbingMetadataResponse,
                    construct_type(
                        type_=DubbingMetadataResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def delete_dubbing_project(
        self,
        dubbing_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> DeleteDubbingResponseModel:
        """
        Deletes a dubbing project.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteDubbingResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.delete_dubbing_project(
                dubbing_id="dubbing_id",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/dubbing/{jsonable_encoder(dubbing_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    DeleteDubbingResponseModel,
                    construct_type(
                        type_=DeleteDubbingResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_dubbed_file(
        self,
        dubbing_id: str,
        language_code: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.AsyncIterator[bytes]:
        """
        Returns dubbed file as a streamed file. Videos will be returned in MP4 format and audio only dubs will be returned in MP3.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        language_code : str
            ID of the language.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Yields
        ------
        typing.AsyncIterator[bytes]
            The dubbed audio or video file
        """
        async with self._client_wrapper.httpx_client.stream(
            f"v1/dubbing/{jsonable_encoder(dubbing_id)}/audio/{jsonable_encoder(language_code)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        ) as _response:
            try:
                if 200 <= _response.status_code < 300:
                    _chunk_size = request_options.get("chunk_size", 1024) if request_options is not None else 1024
                    async for _chunk in _response.aiter_bytes(chunk_size=_chunk_size):
                        yield _chunk
                    return
                await _response.aread()
                if _response.status_code == 403:
                    raise ForbiddenError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 404:
                    raise NotFoundError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 422:
                    raise UnprocessableEntityError(
                        typing.cast(
                            HttpValidationError,
                            construct_type(
                                type_=HttpValidationError,  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                if _response.status_code == 425:
                    raise TooEarlyError(
                        typing.cast(
                            typing.Optional[typing.Any],
                            construct_type(
                                type_=typing.Optional[typing.Any],  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                _response_json = _response.json()
            except JSONDecodeError:
                raise ApiError(status_code=_response.status_code, body=_response.text)
            raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_transcript_for_dub(
        self,
        dubbing_id: str,
        language_code: str,
        *,
        format_type: typing.Optional[DubbingGetTranscriptForDubRequestFormatType] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> str:
        """
        Returns transcript for the dub as an SRT or WEBVTT file.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        language_code : str
            ID of the language.

        format_type : typing.Optional[DubbingGetTranscriptForDubRequestFormatType]
            Format to use for the subtitle file, either 'srt' or 'webvtt'

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        str
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.get_transcript_for_dub(
                dubbing_id="dubbing_id",
                language_code="language_code",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/dubbing/{jsonable_encoder(dubbing_id)}/transcript/{jsonable_encoder(language_code)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            params={
                "format_type": format_type,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    str,
                    construct_type(
                        type_=str,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 403:
                raise ForbiddenError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 404:
                raise NotFoundError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            if _response.status_code == 425:
                raise TooEarlyError(
                    typing.cast(
                        typing.Optional[typing.Any],
                        construct_type(
                            type_=typing.Optional[typing.Any],  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)
