# This file was auto-generated by Fern from our API Definition.

from .types import (
    A<PERSON>ent,
    AddChapterResponseModel,
    AddKnowledgeBaseResponseModel,
    AddProjectResponseModel,
    AddPronunciationDictionaryResponseModel,
    AddPronunciationDictionaryRulesResponseModel,
    AddSharingVoiceRequest,
    AddVoiceIvcResponseModel,
    AddVoiceResponseModel,
    AddWorkspaceGroupMemberResponseModel,
    AddWorkspaceInviteResponseModel,
    AdditionalFormatResponseModel,
    AdditionalFormats,
    Age,
    AgentBan,
    AgentCallLimits,
    AgentConfigApiModelInput,
    AgentConfigApiModelOutput,
    AgentConfigOverride,
    AgentConfigOverrideConfig,
    AgentMetadataResponseModel,
    AgentPlatformSettingsRequestModel,
    AgentPlatformSettingsResponseModel,
    AgentSummaryResponseModel,
    AgentWorkspaceOverridesInput,
    AgentWorkspaceOverridesOutput,
    AllowlistItem,
    ArrayJsonSchemaPropertyInput,
    ArrayJsonSchemaPropertyInputItems,
    ArrayJsonSchemaPropertyOutput,
    ArrayJsonSchemaPropertyOutputItems,
    AsrConversationalConfig,
    AsrInputFormat,
    AsrProvider,
    AsrQuality,
    AudioNativeCreateProjectResponseModel,
    AudioNativeEditContentResponseModel,
    AudioNativeProjectSettingsResponseModel,
    AudioOutput,
    AudioWithTimestampsResponseModel,
    AuthSettings,
    AuthorizationMethod,
    BanReasonType,
    BodyAddToKnowledgeBaseV1ConvaiAddToKnowledgeBasePost,
    BodyAddToKnowledgeBaseV1ConvaiAgentsAgentIdAddToKnowledgeBasePost,
    BreakdownTypes,
    ChapterContentBlockExtendableNodeResponseModel,
    ChapterContentBlockInputModel,
    ChapterContentBlockResponseModel,
    ChapterContentBlockResponseModelNodesItem,
    ChapterContentBlockResponseModelNodesItem_Other,
    ChapterContentBlockResponseModelNodesItem_TtsNode,
    ChapterContentBlockTtsNodeResponseModel,
    ChapterContentInputModel,
    ChapterContentParagraphTtsNodeInputModel,
    ChapterContentResponseModel,
    ChapterResponse,
    ChapterSnapshotExtendedResponseModel,
    ChapterSnapshotResponse,
    ChapterSnapshotsResponse,
    ChapterState,
    ChapterStatisticsResponse,
    ChapterWithContentResponseModel,
    ChapterWithContentResponseModelState,
    CharacterAlignmentModel,
    CharacterAlignmentResponseModel,
    ClientEvent,
    ClientToolConfigInput,
    ClientToolConfigOutput,
    CloseConnection,
    ConvAiSecretLocator,
    ConvAiStoredSecretDependencies,
    ConvAiStoredSecretDependenciesAgentToolsItem,
    ConvAiStoredSecretDependenciesAgentToolsItem_Available,
    ConvAiStoredSecretDependenciesAgentToolsItem_Unknown,
    ConvAiStoredSecretDependenciesToolsItem,
    ConvAiStoredSecretDependenciesToolsItem_Available,
    ConvAiStoredSecretDependenciesToolsItem_Unknown,
    ConvAiWebhooks,
    ConvAiWorkspaceStoredSecretConfig,
    ConversationChargingCommonModel,
    ConversationConfig,
    ConversationConfigClientOverrideConfigInput,
    ConversationConfigClientOverrideConfigOutput,
    ConversationConfigClientOverrideInput,
    ConversationConfigClientOverrideOutput,
    ConversationDeletionSettings,
    ConversationHistoryAnalysisCommonModel,
    ConversationHistoryEvaluationCriteriaResultCommonModel,
    ConversationHistoryFeedbackCommonModel,
    ConversationHistoryMetadataCommonModel,
    ConversationHistoryMetadataCommonModelPhoneCall,
    ConversationHistoryMetadataCommonModelPhoneCall_Twilio,
    ConversationHistoryTranscriptCommonModel,
    ConversationHistoryTranscriptCommonModelRole,
    ConversationHistoryTranscriptToolCallCommonModel,
    ConversationHistoryTranscriptToolResultCommonModel,
    ConversationHistoryTwilioPhoneCallModel,
    ConversationHistoryTwilioPhoneCallModelDirection,
    ConversationInitiationClientDataConfigInput,
    ConversationInitiationClientDataConfigOutput,
    ConversationInitiationClientDataRequestInput,
    ConversationInitiationClientDataRequestInputDynamicVariablesValue,
    ConversationInitiationClientDataRequestOutput,
    ConversationInitiationClientDataRequestOutputDynamicVariablesValue,
    ConversationInitiationClientDataWebhook,
    ConversationInitiationClientDataWebhookRequestHeadersValue,
    ConversationSignedUrlResponseModel,
    ConversationSummaryResponseModel,
    ConversationSummaryResponseModelStatus,
    ConversationTokenDbModel,
    ConversationTokenPurpose,
    ConversationTurnMetrics,
    ConversationalConfigApiModelInput,
    ConversationalConfigApiModelOutput,
    ConvertChapterResponseModel,
    ConvertProjectResponseModel,
    CreateAgentResponseModel,
    CreateAudioNativeProjectRequest,
    CreatePhoneNumberResponseModel,
    CreatePronunciationDictionaryResponseModel,
    CreateSipTrunkPhoneNumberRequest,
    CreateTwilioPhoneNumberRequest,
    Currency,
    CustomLlm,
    DataCollectionResultCommonModel,
    DeleteChapterResponseModel,
    DeleteDubbingResponseModel,
    DeleteHistoryItemResponse,
    DeleteProjectResponseModel,
    DeleteSampleResponseModel,
    DeleteVoiceResponseModel,
    DeleteWorkspaceGroupMemberResponseModel,
    DeleteWorkspaceInviteResponseModel,
    DependentAvailableAgentIdentifier,
    DependentAvailableAgentIdentifierAccessLevel,
    DependentAvailableAgentToolIdentifier,
    DependentAvailableAgentToolIdentifierAccessLevel,
    DependentAvailableToolIdentifier,
    DependentAvailableToolIdentifierAccessLevel,
    DependentPhoneNumberIdentifier,
    DependentUnknownAgentIdentifier,
    DependentUnknownAgentToolIdentifier,
    DependentUnknownToolIdentifier,
    DoDubbingResponse,
    DocumentUsageModeEnum,
    DocxExportOptions,
    DubbedSegment,
    DubbingMediaMetadata,
    DubbingMediaReference,
    DubbingMetadataResponse,
    DubbingResource,
    DynamicVariablesConfig,
    DynamicVariablesConfigDynamicVariablePlaceholdersValue,
    EditChapterResponseModel,
    EditProjectResponseModel,
    EditVoiceResponseModel,
    EditVoiceSettingsResponseModel,
    EmbedVariant,
    EmbeddingModelEnum,
    EvaluationSettings,
    EvaluationSuccessResult,
    ExportOptions,
    ExportOptions_Docx,
    ExportOptions_Html,
    ExportOptions_Pdf,
    ExportOptions_SegmentedJson,
    ExportOptions_Srt,
    ExportOptions_Txt,
    ExtendedSubscriptionResponseModelBillingPeriod,
    ExtendedSubscriptionResponseModelCharacterRefreshPeriod,
    ExtendedSubscriptionResponseModelCurrency,
    FeedbackItem,
    FineTuningResponse,
    FineTuningResponseModelStateValue,
    ForcedAlignmentCharacterResponseModel,
    ForcedAlignmentResponseModel,
    ForcedAlignmentWordResponseModel,
    Gender,
    GenerationConfig,
    GetAgentEmbedResponseModel,
    GetAgentLinkResponseModel,
    GetAgentResponseModel,
    GetAgentsPageResponseModel,
    GetAudioNativeProjectSettingsResponseModel,
    GetChaptersResponse,
    GetConvAiSettingsResponseModel,
    GetConversationResponseModel,
    GetConversationResponseModelStatus,
    GetConversationsPageResponseModel,
    GetKnowledgeBaseDependentAgentsResponseModel,
    GetKnowledgeBaseDependentAgentsResponseModelAgentsItem,
    GetKnowledgeBaseDependentAgentsResponseModelAgentsItem_Available,
    GetKnowledgeBaseDependentAgentsResponseModelAgentsItem_Unknown,
    GetKnowledgeBaseFileResponseModel,
    GetKnowledgeBaseListResponseModel,
    GetKnowledgeBaseListResponseModelDocumentsItem,
    GetKnowledgeBaseListResponseModelDocumentsItem_File,
    GetKnowledgeBaseListResponseModelDocumentsItem_Text,
    GetKnowledgeBaseListResponseModelDocumentsItem_Url,
    GetKnowledgeBaseSummaryFileResponseModel,
    GetKnowledgeBaseSummaryFileResponseModelDependentAgentsItem,
    GetKnowledgeBaseSummaryFileResponseModelDependentAgentsItem_Available,
    GetKnowledgeBaseSummaryFileResponseModelDependentAgentsItem_Unknown,
    GetKnowledgeBaseSummaryTextResponseModel,
    GetKnowledgeBaseSummaryTextResponseModelDependentAgentsItem,
    GetKnowledgeBaseSummaryTextResponseModelDependentAgentsItem_Available,
    GetKnowledgeBaseSummaryTextResponseModelDependentAgentsItem_Unknown,
    GetKnowledgeBaseSummaryUrlResponseModel,
    GetKnowledgeBaseSummaryUrlResponseModelDependentAgentsItem,
    GetKnowledgeBaseSummaryUrlResponseModelDependentAgentsItem_Available,
    GetKnowledgeBaseSummaryUrlResponseModelDependentAgentsItem_Unknown,
    GetKnowledgeBaseTextResponseModel,
    GetKnowledgeBaseUrlResponseModel,
    GetLibraryVoicesResponse,
    GetPhoneNumberResponseModel,
    GetProjectsResponse,
    GetPronunciationDictionariesMetadataResponseModel,
    GetPronunciationDictionaryMetadataResponse,
    GetSpeechHistoryResponse,
    GetVoicesResponse,
    GetVoicesV2ResponseModel,
    GetWorkspaceSecretsResponseModel,
    HistoryAlignmentResponseModel,
    HistoryAlignmentsResponseModel,
    HistoryItem,
    HtmlExportOptions,
    HttpValidationError,
    ImageAvatar,
    InitializeConnection,
    Invoice,
    KnowledgeBaseDocumentChunkResponseModel,
    KnowledgeBaseDocumentMetadataResponseModel,
    KnowledgeBaseDocumentType,
    KnowledgeBaseLocator,
    LanguageAddedResponse,
    LanguagePresetInput,
    LanguagePresetOutput,
    LanguagePresetTranslation,
    LanguageResponse,
    LibraryVoiceResponse,
    LibraryVoiceResponseModelCategory,
    LiteralJsonSchemaProperty,
    LiteralJsonSchemaPropertyConstantValue,
    LiteralJsonSchemaPropertyType,
    Llm,
    ManualVerificationFileResponse,
    ManualVerificationResponse,
    MetricRecord,
    Model,
    ModelRatesResponseModel,
    ModelResponseModelConcurrencyGroup,
    ModerationStatusResponseModel,
    ModerationStatusResponseModelSafetyStatus,
    ModerationStatusResponseModelWarningStatus,
    NormalizedAlignment,
    ObjectJsonSchemaPropertyInput,
    ObjectJsonSchemaPropertyInputPropertiesValue,
    ObjectJsonSchemaPropertyOutput,
    ObjectJsonSchemaPropertyOutputPropertiesValue,
    OrbAvatar,
    OutputFormat,
    PdfExportOptions,
    PhoneNumberAgentInfo,
    PodcastBulletinMode,
    PodcastBulletinModeData,
    PodcastConversationMode,
    PodcastConversationModeData,
    PodcastProjectResponseModel,
    PodcastTextSource,
    PodcastUrlSource,
    PostAgentAvatarResponseModel,
    PostWorkspaceSecretResponseModel,
    PrivacyConfig,
    ProfilePageResponseModel,
    ProjectCreationMetaResponseModel,
    ProjectCreationMetaResponseModelStatus,
    ProjectCreationMetaResponseModelType,
    ProjectExtendedResponseModel,
    ProjectExtendedResponseModelAccessLevel,
    ProjectExtendedResponseModelApplyTextNormalization,
    ProjectExtendedResponseModelFiction,
    ProjectExtendedResponseModelQualityPreset,
    ProjectExtendedResponseModelSourceType,
    ProjectExtendedResponseModelTargetAudience,
    ProjectResponse,
    ProjectResponseModelAccessLevel,
    ProjectResponseModelFiction,
    ProjectResponseModelSourceType,
    ProjectResponseModelTargetAudience,
    ProjectSnapshotExtendedResponseModel,
    ProjectSnapshotResponse,
    ProjectSnapshotsResponse,
    ProjectState,
    PromptAgentInput,
    PromptAgentInputToolsItem,
    PromptAgentInputToolsItem_Client,
    PromptAgentInputToolsItem_System,
    PromptAgentInputToolsItem_Webhook,
    PromptAgentOutput,
    PromptAgentOutputToolsItem,
    PromptAgentOutputToolsItem_Client,
    PromptAgentOutputToolsItem_System,
    PromptAgentOutputToolsItem_Webhook,
    PromptAgentOverride,
    PromptAgentOverrideConfig,
    PromptEvaluationCriteria,
    PronunciationDictionaryAliasRuleRequestModel,
    PronunciationDictionaryPhonemeRuleRequestModel,
    PronunciationDictionaryVersionLocator,
    PronunciationDictionaryVersionResponseModel,
    PydanticPronunciationDictionaryVersionLocator,
    QueryParamsJsonSchema,
    RagChunkMetadata,
    RagConfig,
    RagIndexResponseModel,
    RagIndexStatus,
    RagRetrievalInfo,
    ReaderResourceResponseModel,
    ReaderResourceResponseModelResourceType,
    RealtimeVoiceSettings,
    RecordingResponse,
    RemovePronunciationDictionaryRulesResponseModel,
    ResourceAccessInfo,
    ResourceAccessInfoRole,
    ResourceMetadataResponseModel,
    ReviewStatus,
    SafetyCommonModel,
    SafetyEvaluation,
    SafetyResponseModel,
    SafetyRule,
    SecretDependencyType,
    SegmentCreateResponse,
    SegmentDeleteResponse,
    SegmentDubResponse,
    SegmentTranscriptionResponse,
    SegmentTranslationResponse,
    SegmentUpdateResponse,
    SegmentedJsonExportOptions,
    SendText,
    ShareOptionResponseModel,
    ShareOptionResponseModelType,
    SipTrunkCredentials,
    SpeakerResponseModel,
    SpeakerSegment,
    SpeakerSeparationResponseModel,
    SpeakerSeparationResponseModelStatus,
    SpeakerTrack,
    SpeechHistoryItemResponse,
    SpeechHistoryItemResponseModelSource,
    SpeechHistoryItemResponseModelVoiceCategory,
    SpeechToTextCharacterResponseModel,
    SpeechToTextChunkResponseModel,
    SpeechToTextWordResponseModel,
    SpeechToTextWordResponseModelType,
    SrtExportOptions,
    StreamingAudioChunkWithTimestampsResponseModel,
    Subscription,
    SubscriptionResponse,
    SubscriptionResponseModelBillingPeriod,
    SubscriptionResponseModelCharacterRefreshPeriod,
    SubscriptionResponseModelCurrency,
    SubscriptionStatus,
    SubscriptionUsageResponseModel,
    SystemToolConfig,
    TelephonyProvider,
    TextToSpeechAsStreamRequest,
    TtsConversationalConfig,
    TtsConversationalConfigOverride,
    TtsConversationalConfigOverrideConfig,
    TtsConversationalModel,
    TtsOptimizeStreamingLatency,
    TtsOutputFormat,
    TurnConfig,
    TurnMode,
    TwilioOutboundCallResponse,
    TxtExportOptions,
    UpdateWorkspaceMemberResponseModel,
    UrlAvatar,
    UsageCharactersResponseModel,
    User,
    UserFeedback,
    UserFeedbackScore,
    UtteranceResponseModel,
    ValidationError,
    ValidationErrorLocItem,
    VerificationAttemptResponse,
    VerifiedVoiceLanguageResponseModel,
    Voice,
    VoiceGenerationParameterOptionResponse,
    VoiceGenerationParameterResponse,
    VoicePreviewResponseModel,
    VoicePreviewsResponseModel,
    VoiceResponseModelCategory,
    VoiceResponseModelSafetyControl,
    VoiceSample,
    VoiceSettings,
    VoiceSharingModerationCheckResponseModel,
    VoiceSharingResponse,
    VoiceSharingResponseModelCategory,
    VoiceSharingState,
    VoiceVerificationResponse,
    WebhookToolApiSchemaConfigInput,
    WebhookToolApiSchemaConfigInputMethod,
    WebhookToolApiSchemaConfigInputRequestHeadersValue,
    WebhookToolApiSchemaConfigOutput,
    WebhookToolApiSchemaConfigOutputMethod,
    WebhookToolApiSchemaConfigOutputRequestHeadersValue,
    WebhookToolConfigInput,
    WebhookToolConfigOutput,
    WidgetConfig,
    WidgetConfigAvatar,
    WidgetConfigAvatar_Image,
    WidgetConfigAvatar_Orb,
    WidgetConfigAvatar_Url,
    WidgetConfigResponseModel,
    WidgetConfigResponseModelAvatar,
    WidgetConfigResponseModelAvatar_Image,
    WidgetConfigResponseModelAvatar_Orb,
    WidgetConfigResponseModelAvatar_Url,
    WidgetExpandable,
    WidgetFeedbackMode,
    WorkspaceGroupByNameResponseModel,
    WorkspaceResourceType,
)
from .errors import BadRequestError, ForbiddenError, NotFoundError, TooEarlyError, UnprocessableEntityError
from . import (
    audio_isolation,
    audio_native,
    conversational_ai,
    dubbing,
    forced_alignment,
    history,
    models,
    projects,
    pronunciation_dictionary,
    samples,
    speech_to_speech,
    speech_to_text,
    studio,
    text_to_sound_effects,
    text_to_speech,
    text_to_voice,
    usage,
    user,
    v_1_text_to_speech_stream_input,
    voice_generation,
    voices,
    workspace,
)
from .client import AsyncElevenLabs, ElevenLabs
from .conversational_ai import (
    ConversationalAiCreatePhoneNumberRequestBody,
    ConversationalAiGetKnowledgeBaseDocumentByIdResponse,
    ConversationalAiGetKnowledgeBaseDocumentByIdResponse_File,
    ConversationalAiGetKnowledgeBaseDocumentByIdResponse_Text,
    ConversationalAiGetKnowledgeBaseDocumentByIdResponse_Url,
)
from .dubbing import DubbingGetTranscriptForDubRequestFormatType
from .environment import ElevenLabsEnvironment
from .history import HistoryGetAllRequestSource
from .play import play, save, stream
from .projects import (
    AddProjectV1ProjectsAddPostRequestApplyTextNormalization,
    AddProjectV1ProjectsAddPostRequestFiction,
    AddProjectV1ProjectsAddPostRequestSourceType,
    AddProjectV1ProjectsAddPostRequestTargetAudience,
    BodyCreatePodcastV1ProjectsPodcastCreatePostDurationScale,
    BodyCreatePodcastV1ProjectsPodcastCreatePostMode,
    BodyCreatePodcastV1ProjectsPodcastCreatePostMode_Bulletin,
    BodyCreatePodcastV1ProjectsPodcastCreatePostMode_Conversation,
    BodyCreatePodcastV1ProjectsPodcastCreatePostQualityPreset,
    BodyCreatePodcastV1ProjectsPodcastCreatePostSource,
    BodyCreatePodcastV1ProjectsPodcastCreatePostSourceItem,
    BodyCreatePodcastV1ProjectsPodcastCreatePostSourceItem_Text,
    BodyCreatePodcastV1ProjectsPodcastCreatePostSourceItem_Url,
)
from .pronunciation_dictionary import (
    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem,
    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Alias,
    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Phoneme,
    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostWorkspaceAccess,
    PronunciationDictionaryAddFromFileRequestWorkspaceAccess,
    PronunciationDictionaryGetAllRequestSort,
    PronunciationDictionaryRule,
    PronunciationDictionaryRule_Alias,
    PronunciationDictionaryRule_Phoneme,
)
from .speech_to_text import SpeechToTextConvertRequestTimestampsGranularity
from .studio import (
    BodyCreatePodcastV1StudioPodcastsPostDurationScale,
    BodyCreatePodcastV1StudioPodcastsPostMode,
    BodyCreatePodcastV1StudioPodcastsPostMode_Bulletin,
    BodyCreatePodcastV1StudioPodcastsPostMode_Conversation,
    BodyCreatePodcastV1StudioPodcastsPostQualityPreset,
    BodyCreatePodcastV1StudioPodcastsPostSource,
    BodyCreatePodcastV1StudioPodcastsPostSourceItem,
    BodyCreatePodcastV1StudioPodcastsPostSourceItem_Text,
    BodyCreatePodcastV1StudioPodcastsPostSourceItem_Url,
)
from .text_to_sound_effects import TextToSoundEffectsConvertRequestOutputFormat
from .text_to_speech import (
    BodyTextToSpeechStreamingV1TextToSpeechVoiceIdStreamPostApplyTextNormalization,
    BodyTextToSpeechStreamingWithTimestampsV1TextToSpeechVoiceIdStreamWithTimestampsPostApplyTextNormalization,
    BodyTextToSpeechV1TextToSpeechVoiceIdPostApplyTextNormalization,
    BodyTextToSpeechWithTimestampsV1TextToSpeechVoiceIdWithTimestampsPostApplyTextNormalization,
)
from .text_to_voice import TextToVoiceCreatePreviewsRequestOutputFormat
from .v_1_text_to_speech_stream_input import SendMessage
from .version import __version__
from .voices import VoicesGetSharedRequestCategory
from .workspace import (
    BodyShareWorkspaceResourceV1WorkspaceResourcesResourceIdSharePostRole,
    BodyUpdateMemberV1WorkspaceMembersPostWorkspaceRole,
)

__all__ = [
    "Accent",
    "AddChapterResponseModel",
    "AddKnowledgeBaseResponseModel",
    "AddProjectResponseModel",
    "AddProjectV1ProjectsAddPostRequestApplyTextNormalization",
    "AddProjectV1ProjectsAddPostRequestFiction",
    "AddProjectV1ProjectsAddPostRequestSourceType",
    "AddProjectV1ProjectsAddPostRequestTargetAudience",
    "AddPronunciationDictionaryResponseModel",
    "AddPronunciationDictionaryRulesResponseModel",
    "AddSharingVoiceRequest",
    "AddVoiceIvcResponseModel",
    "AddVoiceResponseModel",
    "AddWorkspaceGroupMemberResponseModel",
    "AddWorkspaceInviteResponseModel",
    "AdditionalFormatResponseModel",
    "AdditionalFormats",
    "Age",
    "AgentBan",
    "AgentCallLimits",
    "AgentConfigApiModelInput",
    "AgentConfigApiModelOutput",
    "AgentConfigOverride",
    "AgentConfigOverrideConfig",
    "AgentMetadataResponseModel",
    "AgentPlatformSettingsRequestModel",
    "AgentPlatformSettingsResponseModel",
    "AgentSummaryResponseModel",
    "AgentWorkspaceOverridesInput",
    "AgentWorkspaceOverridesOutput",
    "AllowlistItem",
    "ArrayJsonSchemaPropertyInput",
    "ArrayJsonSchemaPropertyInputItems",
    "ArrayJsonSchemaPropertyOutput",
    "ArrayJsonSchemaPropertyOutputItems",
    "AsrConversationalConfig",
    "AsrInputFormat",
    "AsrProvider",
    "AsrQuality",
    "AsyncElevenLabs",
    "AudioNativeCreateProjectResponseModel",
    "AudioNativeEditContentResponseModel",
    "AudioNativeProjectSettingsResponseModel",
    "AudioOutput",
    "AudioWithTimestampsResponseModel",
    "AuthSettings",
    "AuthorizationMethod",
    "BadRequestError",
    "BanReasonType",
    "BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem",
    "BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Alias",
    "BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Phoneme",
    "BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostWorkspaceAccess",
    "BodyAddToKnowledgeBaseV1ConvaiAddToKnowledgeBasePost",
    "BodyAddToKnowledgeBaseV1ConvaiAgentsAgentIdAddToKnowledgeBasePost",
    "BodyCreatePodcastV1ProjectsPodcastCreatePostDurationScale",
    "BodyCreatePodcastV1ProjectsPodcastCreatePostMode",
    "BodyCreatePodcastV1ProjectsPodcastCreatePostMode_Bulletin",
    "BodyCreatePodcastV1ProjectsPodcastCreatePostMode_Conversation",
    "BodyCreatePodcastV1ProjectsPodcastCreatePostQualityPreset",
    "BodyCreatePodcastV1ProjectsPodcastCreatePostSource",
    "BodyCreatePodcastV1ProjectsPodcastCreatePostSourceItem",
    "BodyCreatePodcastV1ProjectsPodcastCreatePostSourceItem_Text",
    "BodyCreatePodcastV1ProjectsPodcastCreatePostSourceItem_Url",
    "BodyCreatePodcastV1StudioPodcastsPostDurationScale",
    "BodyCreatePodcastV1StudioPodcastsPostMode",
    "BodyCreatePodcastV1StudioPodcastsPostMode_Bulletin",
    "BodyCreatePodcastV1StudioPodcastsPostMode_Conversation",
    "BodyCreatePodcastV1StudioPodcastsPostQualityPreset",
    "BodyCreatePodcastV1StudioPodcastsPostSource",
    "BodyCreatePodcastV1StudioPodcastsPostSourceItem",
    "BodyCreatePodcastV1StudioPodcastsPostSourceItem_Text",
    "BodyCreatePodcastV1StudioPodcastsPostSourceItem_Url",
    "BodyShareWorkspaceResourceV1WorkspaceResourcesResourceIdSharePostRole",
    "BodyTextToSpeechStreamingV1TextToSpeechVoiceIdStreamPostApplyTextNormalization",
    "BodyTextToSpeechStreamingWithTimestampsV1TextToSpeechVoiceIdStreamWithTimestampsPostApplyTextNormalization",
    "BodyTextToSpeechV1TextToSpeechVoiceIdPostApplyTextNormalization",
    "BodyTextToSpeechWithTimestampsV1TextToSpeechVoiceIdWithTimestampsPostApplyTextNormalization",
    "BodyUpdateMemberV1WorkspaceMembersPostWorkspaceRole",
    "BreakdownTypes",
    "ChapterContentBlockExtendableNodeResponseModel",
    "ChapterContentBlockInputModel",
    "ChapterContentBlockResponseModel",
    "ChapterContentBlockResponseModelNodesItem",
    "ChapterContentBlockResponseModelNodesItem_Other",
    "ChapterContentBlockResponseModelNodesItem_TtsNode",
    "ChapterContentBlockTtsNodeResponseModel",
    "ChapterContentInputModel",
    "ChapterContentParagraphTtsNodeInputModel",
    "ChapterContentResponseModel",
    "ChapterResponse",
    "ChapterSnapshotExtendedResponseModel",
    "ChapterSnapshotResponse",
    "ChapterSnapshotsResponse",
    "ChapterState",
    "ChapterStatisticsResponse",
    "ChapterWithContentResponseModel",
    "ChapterWithContentResponseModelState",
    "CharacterAlignmentModel",
    "CharacterAlignmentResponseModel",
    "ClientEvent",
    "ClientToolConfigInput",
    "ClientToolConfigOutput",
    "CloseConnection",
    "ConvAiSecretLocator",
    "ConvAiStoredSecretDependencies",
    "ConvAiStoredSecretDependenciesAgentToolsItem",
    "ConvAiStoredSecretDependenciesAgentToolsItem_Available",
    "ConvAiStoredSecretDependenciesAgentToolsItem_Unknown",
    "ConvAiStoredSecretDependenciesToolsItem",
    "ConvAiStoredSecretDependenciesToolsItem_Available",
    "ConvAiStoredSecretDependenciesToolsItem_Unknown",
    "ConvAiWebhooks",
    "ConvAiWorkspaceStoredSecretConfig",
    "ConversationChargingCommonModel",
    "ConversationConfig",
    "ConversationConfigClientOverrideConfigInput",
    "ConversationConfigClientOverrideConfigOutput",
    "ConversationConfigClientOverrideInput",
    "ConversationConfigClientOverrideOutput",
    "ConversationDeletionSettings",
    "ConversationHistoryAnalysisCommonModel",
    "ConversationHistoryEvaluationCriteriaResultCommonModel",
    "ConversationHistoryFeedbackCommonModel",
    "ConversationHistoryMetadataCommonModel",
    "ConversationHistoryMetadataCommonModelPhoneCall",
    "ConversationHistoryMetadataCommonModelPhoneCall_Twilio",
    "ConversationHistoryTranscriptCommonModel",
    "ConversationHistoryTranscriptCommonModelRole",
    "ConversationHistoryTranscriptToolCallCommonModel",
    "ConversationHistoryTranscriptToolResultCommonModel",
    "ConversationHistoryTwilioPhoneCallModel",
    "ConversationHistoryTwilioPhoneCallModelDirection",
    "ConversationInitiationClientDataConfigInput",
    "ConversationInitiationClientDataConfigOutput",
    "ConversationInitiationClientDataRequestInput",
    "ConversationInitiationClientDataRequestInputDynamicVariablesValue",
    "ConversationInitiationClientDataRequestOutput",
    "ConversationInitiationClientDataRequestOutputDynamicVariablesValue",
    "ConversationInitiationClientDataWebhook",
    "ConversationInitiationClientDataWebhookRequestHeadersValue",
    "ConversationSignedUrlResponseModel",
    "ConversationSummaryResponseModel",
    "ConversationSummaryResponseModelStatus",
    "ConversationTokenDbModel",
    "ConversationTokenPurpose",
    "ConversationTurnMetrics",
    "ConversationalAiCreatePhoneNumberRequestBody",
    "ConversationalAiGetKnowledgeBaseDocumentByIdResponse",
    "ConversationalAiGetKnowledgeBaseDocumentByIdResponse_File",
    "ConversationalAiGetKnowledgeBaseDocumentByIdResponse_Text",
    "ConversationalAiGetKnowledgeBaseDocumentByIdResponse_Url",
    "ConversationalConfigApiModelInput",
    "ConversationalConfigApiModelOutput",
    "ConvertChapterResponseModel",
    "ConvertProjectResponseModel",
    "CreateAgentResponseModel",
    "CreateAudioNativeProjectRequest",
    "CreatePhoneNumberResponseModel",
    "CreatePronunciationDictionaryResponseModel",
    "CreateSipTrunkPhoneNumberRequest",
    "CreateTwilioPhoneNumberRequest",
    "Currency",
    "CustomLlm",
    "DataCollectionResultCommonModel",
    "DeleteChapterResponseModel",
    "DeleteDubbingResponseModel",
    "DeleteHistoryItemResponse",
    "DeleteProjectResponseModel",
    "DeleteSampleResponseModel",
    "DeleteVoiceResponseModel",
    "DeleteWorkspaceGroupMemberResponseModel",
    "DeleteWorkspaceInviteResponseModel",
    "DependentAvailableAgentIdentifier",
    "DependentAvailableAgentIdentifierAccessLevel",
    "DependentAvailableAgentToolIdentifier",
    "DependentAvailableAgentToolIdentifierAccessLevel",
    "DependentAvailableToolIdentifier",
    "DependentAvailableToolIdentifierAccessLevel",
    "DependentPhoneNumberIdentifier",
    "DependentUnknownAgentIdentifier",
    "DependentUnknownAgentToolIdentifier",
    "DependentUnknownToolIdentifier",
    "DoDubbingResponse",
    "DocumentUsageModeEnum",
    "DocxExportOptions",
    "DubbedSegment",
    "DubbingGetTranscriptForDubRequestFormatType",
    "DubbingMediaMetadata",
    "DubbingMediaReference",
    "DubbingMetadataResponse",
    "DubbingResource",
    "DynamicVariablesConfig",
    "DynamicVariablesConfigDynamicVariablePlaceholdersValue",
    "EditChapterResponseModel",
    "EditProjectResponseModel",
    "EditVoiceResponseModel",
    "EditVoiceSettingsResponseModel",
    "ElevenLabs",
    "ElevenLabsEnvironment",
    "EmbedVariant",
    "EmbeddingModelEnum",
    "EvaluationSettings",
    "EvaluationSuccessResult",
    "ExportOptions",
    "ExportOptions_Docx",
    "ExportOptions_Html",
    "ExportOptions_Pdf",
    "ExportOptions_SegmentedJson",
    "ExportOptions_Srt",
    "ExportOptions_Txt",
    "ExtendedSubscriptionResponseModelBillingPeriod",
    "ExtendedSubscriptionResponseModelCharacterRefreshPeriod",
    "ExtendedSubscriptionResponseModelCurrency",
    "FeedbackItem",
    "FineTuningResponse",
    "FineTuningResponseModelStateValue",
    "ForbiddenError",
    "ForcedAlignmentCharacterResponseModel",
    "ForcedAlignmentResponseModel",
    "ForcedAlignmentWordResponseModel",
    "Gender",
    "GenerationConfig",
    "GetAgentEmbedResponseModel",
    "GetAgentLinkResponseModel",
    "GetAgentResponseModel",
    "GetAgentsPageResponseModel",
    "GetAudioNativeProjectSettingsResponseModel",
    "GetChaptersResponse",
    "GetConvAiSettingsResponseModel",
    "GetConversationResponseModel",
    "GetConversationResponseModelStatus",
    "GetConversationsPageResponseModel",
    "GetKnowledgeBaseDependentAgentsResponseModel",
    "GetKnowledgeBaseDependentAgentsResponseModelAgentsItem",
    "GetKnowledgeBaseDependentAgentsResponseModelAgentsItem_Available",
    "GetKnowledgeBaseDependentAgentsResponseModelAgentsItem_Unknown",
    "GetKnowledgeBaseFileResponseModel",
    "GetKnowledgeBaseListResponseModel",
    "GetKnowledgeBaseListResponseModelDocumentsItem",
    "GetKnowledgeBaseListResponseModelDocumentsItem_File",
    "GetKnowledgeBaseListResponseModelDocumentsItem_Text",
    "GetKnowledgeBaseListResponseModelDocumentsItem_Url",
    "GetKnowledgeBaseSummaryFileResponseModel",
    "GetKnowledgeBaseSummaryFileResponseModelDependentAgentsItem",
    "GetKnowledgeBaseSummaryFileResponseModelDependentAgentsItem_Available",
    "GetKnowledgeBaseSummaryFileResponseModelDependentAgentsItem_Unknown",
    "GetKnowledgeBaseSummaryTextResponseModel",
    "GetKnowledgeBaseSummaryTextResponseModelDependentAgentsItem",
    "GetKnowledgeBaseSummaryTextResponseModelDependentAgentsItem_Available",
    "GetKnowledgeBaseSummaryTextResponseModelDependentAgentsItem_Unknown",
    "GetKnowledgeBaseSummaryUrlResponseModel",
    "GetKnowledgeBaseSummaryUrlResponseModelDependentAgentsItem",
    "GetKnowledgeBaseSummaryUrlResponseModelDependentAgentsItem_Available",
    "GetKnowledgeBaseSummaryUrlResponseModelDependentAgentsItem_Unknown",
    "GetKnowledgeBaseTextResponseModel",
    "GetKnowledgeBaseUrlResponseModel",
    "GetLibraryVoicesResponse",
    "GetPhoneNumberResponseModel",
    "GetProjectsResponse",
    "GetPronunciationDictionariesMetadataResponseModel",
    "GetPronunciationDictionaryMetadataResponse",
    "GetSpeechHistoryResponse",
    "GetVoicesResponse",
    "GetVoicesV2ResponseModel",
    "GetWorkspaceSecretsResponseModel",
    "HistoryAlignmentResponseModel",
    "HistoryAlignmentsResponseModel",
    "HistoryGetAllRequestSource",
    "HistoryItem",
    "HtmlExportOptions",
    "HttpValidationError",
    "ImageAvatar",
    "InitializeConnection",
    "Invoice",
    "KnowledgeBaseDocumentChunkResponseModel",
    "KnowledgeBaseDocumentMetadataResponseModel",
    "KnowledgeBaseDocumentType",
    "KnowledgeBaseLocator",
    "LanguageAddedResponse",
    "LanguagePresetInput",
    "LanguagePresetOutput",
    "LanguagePresetTranslation",
    "LanguageResponse",
    "LibraryVoiceResponse",
    "LibraryVoiceResponseModelCategory",
    "LiteralJsonSchemaProperty",
    "LiteralJsonSchemaPropertyConstantValue",
    "LiteralJsonSchemaPropertyType",
    "Llm",
    "ManualVerificationFileResponse",
    "ManualVerificationResponse",
    "MetricRecord",
    "Model",
    "ModelRatesResponseModel",
    "ModelResponseModelConcurrencyGroup",
    "ModerationStatusResponseModel",
    "ModerationStatusResponseModelSafetyStatus",
    "ModerationStatusResponseModelWarningStatus",
    "NormalizedAlignment",
    "NotFoundError",
    "ObjectJsonSchemaPropertyInput",
    "ObjectJsonSchemaPropertyInputPropertiesValue",
    "ObjectJsonSchemaPropertyOutput",
    "ObjectJsonSchemaPropertyOutputPropertiesValue",
    "OrbAvatar",
    "OutputFormat",
    "PdfExportOptions",
    "PhoneNumberAgentInfo",
    "PodcastBulletinMode",
    "PodcastBulletinModeData",
    "PodcastConversationMode",
    "PodcastConversationModeData",
    "PodcastProjectResponseModel",
    "PodcastTextSource",
    "PodcastUrlSource",
    "PostAgentAvatarResponseModel",
    "PostWorkspaceSecretResponseModel",
    "PrivacyConfig",
    "ProfilePageResponseModel",
    "ProjectCreationMetaResponseModel",
    "ProjectCreationMetaResponseModelStatus",
    "ProjectCreationMetaResponseModelType",
    "ProjectExtendedResponseModel",
    "ProjectExtendedResponseModelAccessLevel",
    "ProjectExtendedResponseModelApplyTextNormalization",
    "ProjectExtendedResponseModelFiction",
    "ProjectExtendedResponseModelQualityPreset",
    "ProjectExtendedResponseModelSourceType",
    "ProjectExtendedResponseModelTargetAudience",
    "ProjectResponse",
    "ProjectResponseModelAccessLevel",
    "ProjectResponseModelFiction",
    "ProjectResponseModelSourceType",
    "ProjectResponseModelTargetAudience",
    "ProjectSnapshotExtendedResponseModel",
    "ProjectSnapshotResponse",
    "ProjectSnapshotsResponse",
    "ProjectState",
    "PromptAgentInput",
    "PromptAgentInputToolsItem",
    "PromptAgentInputToolsItem_Client",
    "PromptAgentInputToolsItem_System",
    "PromptAgentInputToolsItem_Webhook",
    "PromptAgentOutput",
    "PromptAgentOutputToolsItem",
    "PromptAgentOutputToolsItem_Client",
    "PromptAgentOutputToolsItem_System",
    "PromptAgentOutputToolsItem_Webhook",
    "PromptAgentOverride",
    "PromptAgentOverrideConfig",
    "PromptEvaluationCriteria",
    "PronunciationDictionaryAddFromFileRequestWorkspaceAccess",
    "PronunciationDictionaryAliasRuleRequestModel",
    "PronunciationDictionaryGetAllRequestSort",
    "PronunciationDictionaryPhonemeRuleRequestModel",
    "PronunciationDictionaryRule",
    "PronunciationDictionaryRule_Alias",
    "PronunciationDictionaryRule_Phoneme",
    "PronunciationDictionaryVersionLocator",
    "PronunciationDictionaryVersionResponseModel",
    "PydanticPronunciationDictionaryVersionLocator",
    "QueryParamsJsonSchema",
    "RagChunkMetadata",
    "RagConfig",
    "RagIndexResponseModel",
    "RagIndexStatus",
    "RagRetrievalInfo",
    "ReaderResourceResponseModel",
    "ReaderResourceResponseModelResourceType",
    "RealtimeVoiceSettings",
    "RecordingResponse",
    "RemovePronunciationDictionaryRulesResponseModel",
    "ResourceAccessInfo",
    "ResourceAccessInfoRole",
    "ResourceMetadataResponseModel",
    "ReviewStatus",
    "SafetyCommonModel",
    "SafetyEvaluation",
    "SafetyResponseModel",
    "SafetyRule",
    "SecretDependencyType",
    "SegmentCreateResponse",
    "SegmentDeleteResponse",
    "SegmentDubResponse",
    "SegmentTranscriptionResponse",
    "SegmentTranslationResponse",
    "SegmentUpdateResponse",
    "SegmentedJsonExportOptions",
    "SendMessage",
    "SendText",
    "ShareOptionResponseModel",
    "ShareOptionResponseModelType",
    "SipTrunkCredentials",
    "SpeakerResponseModel",
    "SpeakerSegment",
    "SpeakerSeparationResponseModel",
    "SpeakerSeparationResponseModelStatus",
    "SpeakerTrack",
    "SpeechHistoryItemResponse",
    "SpeechHistoryItemResponseModelSource",
    "SpeechHistoryItemResponseModelVoiceCategory",
    "SpeechToTextCharacterResponseModel",
    "SpeechToTextChunkResponseModel",
    "SpeechToTextConvertRequestTimestampsGranularity",
    "SpeechToTextWordResponseModel",
    "SpeechToTextWordResponseModelType",
    "SrtExportOptions",
    "StreamingAudioChunkWithTimestampsResponseModel",
    "Subscription",
    "SubscriptionResponse",
    "SubscriptionResponseModelBillingPeriod",
    "SubscriptionResponseModelCharacterRefreshPeriod",
    "SubscriptionResponseModelCurrency",
    "SubscriptionStatus",
    "SubscriptionUsageResponseModel",
    "SystemToolConfig",
    "TelephonyProvider",
    "TextToSoundEffectsConvertRequestOutputFormat",
    "TextToSpeechAsStreamRequest",
    "TextToVoiceCreatePreviewsRequestOutputFormat",
    "TooEarlyError",
    "TtsConversationalConfig",
    "TtsConversationalConfigOverride",
    "TtsConversationalConfigOverrideConfig",
    "TtsConversationalModel",
    "TtsOptimizeStreamingLatency",
    "TtsOutputFormat",
    "TurnConfig",
    "TurnMode",
    "TwilioOutboundCallResponse",
    "TxtExportOptions",
    "UnprocessableEntityError",
    "UpdateWorkspaceMemberResponseModel",
    "UrlAvatar",
    "UsageCharactersResponseModel",
    "User",
    "UserFeedback",
    "UserFeedbackScore",
    "UtteranceResponseModel",
    "ValidationError",
    "ValidationErrorLocItem",
    "VerificationAttemptResponse",
    "VerifiedVoiceLanguageResponseModel",
    "Voice",
    "VoiceGenerationParameterOptionResponse",
    "VoiceGenerationParameterResponse",
    "VoicePreviewResponseModel",
    "VoicePreviewsResponseModel",
    "VoiceResponseModelCategory",
    "VoiceResponseModelSafetyControl",
    "VoiceSample",
    "VoiceSettings",
    "VoiceSharingModerationCheckResponseModel",
    "VoiceSharingResponse",
    "VoiceSharingResponseModelCategory",
    "VoiceSharingState",
    "VoiceVerificationResponse",
    "VoicesGetSharedRequestCategory",
    "WebhookToolApiSchemaConfigInput",
    "WebhookToolApiSchemaConfigInputMethod",
    "WebhookToolApiSchemaConfigInputRequestHeadersValue",
    "WebhookToolApiSchemaConfigOutput",
    "WebhookToolApiSchemaConfigOutputMethod",
    "WebhookToolApiSchemaConfigOutputRequestHeadersValue",
    "WebhookToolConfigInput",
    "WebhookToolConfigOutput",
    "WidgetConfig",
    "WidgetConfigAvatar",
    "WidgetConfigAvatar_Image",
    "WidgetConfigAvatar_Orb",
    "WidgetConfigAvatar_Url",
    "WidgetConfigResponseModel",
    "WidgetConfigResponseModelAvatar",
    "WidgetConfigResponseModelAvatar_Image",
    "WidgetConfigResponseModelAvatar_Orb",
    "WidgetConfigResponseModelAvatar_Url",
    "WidgetExpandable",
    "WidgetFeedbackMode",
    "WorkspaceGroupByNameResponseModel",
    "WorkspaceResourceType",
    "__version__",
    "audio_isolation",
    "audio_native",
    "conversational_ai",
    "dubbing",
    "forced_alignment",
    "history",
    "models",
    "play",
    "projects",
    "pronunciation_dictionary",
    "samples",
    "save",
    "speech_to_speech",
    "speech_to_text",
    "stream",
    "studio",
    "text_to_sound_effects",
    "text_to_speech",
    "text_to_voice",
    "usage",
    "user",
    "v_1_text_to_speech_stream_input",
    "voice_generation",
    "voices",
    "workspace",
]
