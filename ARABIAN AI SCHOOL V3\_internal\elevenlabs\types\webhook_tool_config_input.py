# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
from .array_json_schema_property_input import ArrayJsonSchemaPropertyInput
from .object_json_schema_property_input import ObjectJsonSchemaPropertyInput
from .webhook_tool_api_schema_config_input import WebhookToolApiSchemaConfigInput
import pydantic
import typing
from .dynamic_variables_config import DynamicVariablesConfig
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class WebhookToolConfigInput(UncheckedBaseModel):
    """
    A webhook tool is a tool that calls an external webhook from our server
    """

    name: str
    description: str
    api_schema: WebhookToolApiSchemaConfigInput = pydantic.Field()
    """
    The schema for the outgoing webhoook, including parameters and URL specification
    """

    dynamic_variables: typing.Optional[DynamicVariablesConfig] = pydantic.Field(default=None)
    """
    Configuration for dynamic variables
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
