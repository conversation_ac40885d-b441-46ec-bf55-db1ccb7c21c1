# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import pydantic
import typing
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class SpeechToTextCharacterResponseModel(UncheckedBaseModel):
    text: str = pydantic.Field()
    """
    The character that was transcribed.
    """

    start: typing.Optional[float] = pydantic.Field(default=None)
    """
    The start time of the character in seconds.
    """

    end: typing.Optional[float] = pydantic.Field(default=None)
    """
    The end time of the character in seconds.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
