"""
خدمة الذكاء الاصطناعي الأساسية
Basic AI Service
"""

import requests
import json
import os
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)


class AIService:
    """خدمة الذكاء الاصطناعي الأساسية"""
    
    def __init__(self):
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.gemini_api_key = os.getenv("GEMINI_API_KEY")
        self.elevenlabs_api_key = os.getenv("ELEVENLABS_API_KEY")
    
    def generate_text_with_openai(self, prompt: str, max_tokens: int = 1000) -> Optional[str]:
        """توليد النص باستخدام OpenAI"""
        if not self.openai_api_key:
            logger.error("OpenAI API key not found")
            return None
        
        try:
            headers = {
                "Authorization": f"Bearer {self.openai_api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": "gpt-3.5-turbo",
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "max_tokens": max_tokens,
                "temperature": 0.7
            }
            
            response = requests.post(
                "https://api.openai.com/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result["choices"][0]["message"]["content"]
            else:
                logger.error(f"OpenAI API error: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error generating text with OpenAI: {e}")
            return None
    
    def generate_text_with_gemini(self, prompt: str) -> Optional[str]:
        """توليد النص باستخدام Google Gemini"""
        if not self.gemini_api_key:
            logger.error("Gemini API key not found")
            return None
        
        try:
            url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={self.gemini_api_key}"
            
            headers = {
                "Content-Type": "application/json"
            }
            
            data = {
                "contents": [
                    {
                        "parts": [
                            {"text": prompt}
                        ]
                    }
                ]
            }
            
            response = requests.post(url, headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if "candidates" in result and len(result["candidates"]) > 0:
                    return result["candidates"][0]["content"]["parts"][0]["text"]
            else:
                logger.error(f"Gemini API error: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error generating text with Gemini: {e}")
            return None
    
    def text_to_speech_elevenlabs(self, text: str, voice_id: str = "21m00Tcm4TlvDq8ikWAM") -> Optional[bytes]:
        """تحويل النص إلى كلام باستخدام ElevenLabs"""
        if not self.elevenlabs_api_key:
            logger.error("ElevenLabs API key not found")
            return None
        
        try:
            url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}"
            
            headers = {
                "Accept": "audio/mpeg",
                "Content-Type": "application/json",
                "xi-api-key": self.elevenlabs_api_key
            }
            
            data = {
                "text": text,
                "model_id": "eleven_multilingual_v2",
                "voice_settings": {
                    "stability": 0.5,
                    "similarity_boost": 0.8
                }
            }
            
            response = requests.post(url, json=data, headers=headers, timeout=60)
            
            if response.status_code == 200:
                return response.content
            else:
                logger.error(f"ElevenLabs API error: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error generating speech with ElevenLabs: {e}")
            return None
    
    def generate_image_prompt(self, topic: str) -> str:
        """توليد وصف للصورة"""
        prompt = f"""
        اكتب وصفاً مفصلاً لصورة تناسب الموضوع التالي: {topic}
        
        يجب أن يكون الوصف:
        - واضح ومفصل
        - مناسب للمحتوى التعليمي
        - جذاب بصرياً
        - باللغة الإنجليزية
        
        اكتب الوصف فقط بدون أي إضافات:
        """
        
        return self.generate_text_with_gemini(prompt) or self.generate_text_with_openai(prompt)
    
    def create_educational_script(self, topic: str) -> Optional[Dict[str, Any]]:
        """إنشاء سكريبت تعليمي"""
        prompt = f"""
        اكتب سكريبت تعليمي قصير عن الموضوع التالي: {topic}
        
        المتطلبات:
        - مدة 30-60 ثانية
        - لغة عربية واضحة
        - محتوى تعليمي مفيد
        - مناسب للفيديوهات القصيرة
        - مقسم إلى 3-5 جمل
        
        اكتب النص فقط بدون أي إضافات أو تنسيق:
        """
        
        text = self.generate_text_with_gemini(prompt) or self.generate_text_with_openai(prompt)
        
        if text:
            # تقسيم النص إلى جمل
            sentences = [s.strip() for s in text.split('.') if s.strip()]
            
            return {
                "full_text": text,
                "sentences": sentences,
                "duration_estimate": len(text.split()) * 0.5,  # تقدير تقريبي
                "word_count": len(text.split())
            }
        
        return None


# إنشاء مثيل من الخدمة
ai_service = AIService()
