# This file was auto-generated by <PERSON>rn from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from .get_knowledge_base_dependent_agents_response_model_agents_item import (
    GetKnowledgeBaseDependentAgentsResponseModelAgentsItem,
)
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic


class GetKnowledgeBaseDependentAgentsResponseModel(UncheckedBaseModel):
    agents: typing.List[GetKnowledgeBaseDependentAgentsResponseModelAgentsItem]
    next_cursor: typing.Optional[str] = None
    has_more: bool

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
