# ستوديو الربيع للأتمتة - متغيرات البيئة
# Spring Automation Studio - Environment Variables

# ==============================================
# إعدادات أساسية / Basic Settings
# ==============================================
APP_NAME="ستوديو الربيع للأتمتة"
APP_VERSION="2.0.0"
DEBUG=true
SECRET_KEY="your-super-secret-key-change-this-in-production"
ENVIRONMENT="development"

# ==============================================
# إعدادات الخادم / Server Settings
# ==============================================
HOST="0.0.0.0"
PORT=8000
ALLOWED_HOSTS="localhost,127.0.0.1,0.0.0.0"

# ==============================================
# إعدادات قاعدة البيانات / Database Settings
# ==============================================
DATABASE_URL="postgresql://username:password@localhost:5432/spring_studio"
DATABASE_ECHO=false

# ==============================================
# إعدادات Redis / Redis Settings
# ==============================================
REDIS_URL="redis://localhost:6379"
REDIS_DB=0

# ==============================================
# إعدادات JWT / JWT Settings
# ==============================================
JWT_SECRET_KEY="your-jwt-secret-key-change-this"
JWT_ALGORITHM="HS256"
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# ==============================================
# مفاتيح خدمات الذكاء الاصطناعي / AI Service Keys
# ==============================================

# OpenAI API Key
OPENAI_API_KEY="sk-your-openai-api-key"
OPENAI_MODEL="gpt-4-turbo-preview"
OPENAI_MAX_TOKENS=4000
OPENAI_TEMPERATURE=0.7

# Google Gemini API Key
GEMINI_API_KEY="your-gemini-api-key"

# ElevenLabs API Key
ELEVENLABS_API_KEY="your-elevenlabs-api-key"
ELEVENLABS_MODEL="eleven_multilingual_v2"
ELEVENLABS_VOICE_STABILITY=0.5
ELEVENLABS_VOICE_SIMILARITY=0.8

# Replicate API Token
REPLICATE_API_TOKEN="your-replicate-api-token"

# ==============================================
# إعدادات الملفات / File Settings
# ==============================================
UPLOAD_DIR="uploads"
OUTPUT_DIR="outputs"
TEMP_DIR="temp"
MAX_FILE_SIZE=104857600  # 100MB in bytes
ALLOWED_FILE_TYPES="jpg,jpeg,png,gif,mp4,mp3,wav,txt,pdf"

# ==============================================
# إعدادات الفيديو / Video Settings
# ==============================================
VIDEO_MAX_DURATION=300  # 5 minutes
VIDEO_DEFAULT_FPS=30
VIDEO_DEFAULT_RESOLUTION="1080x1920"
VIDEO_FORMATS="mp4,avi,mov"

# ==============================================
# إعدادات الصوت / Audio Settings
# ==============================================
AUDIO_SAMPLE_RATE=44100
AUDIO_CHANNELS=2
AUDIO_FORMATS="mp3,wav,aac"

# ==============================================
# إعدادات التخزين المؤقت / Cache Settings
# ==============================================
CACHE_TTL=3600  # 1 hour
CACHE_MAX_SIZE=1000

# ==============================================
# إعدادات Celery / Celery Settings
# ==============================================
CELERY_BROKER_URL="redis://localhost:6379/1"
CELERY_RESULT_BACKEND="redis://localhost:6379/1"

# ==============================================
# إعدادات البريد الإلكتروني / Email Settings
# ==============================================
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USERNAME="<EMAIL>"
SMTP_PASSWORD="your-email-password"
SMTP_USE_TLS=true

# ==============================================
# إعدادات المراقبة / Monitoring Settings
# ==============================================
SENTRY_DSN="your-sentry-dsn"
LOG_LEVEL="INFO"
LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# ==============================================
# إعدادات الأمان / Security Settings
# ==============================================
CORS_ORIGINS="http://localhost:3000,http://127.0.0.1:3000"
RATE_LIMIT_PER_MINUTE=60

# ==============================================
# إعدادات النسخ الاحتياطي / Backup Settings
# ==============================================
BACKUP_ENABLED=true
BACKUP_INTERVAL_HOURS=24
BACKUP_RETENTION_DAYS=30

# ==============================================
# إعدادات واجهة المستخدم / Frontend Settings
# ==============================================
REACT_APP_API_URL="http://localhost:8000/api/v1"
REACT_APP_WS_URL="ws://localhost:8000/ws"
REACT_APP_APP_NAME="ستوديو الربيع للأتمتة"
REACT_APP_VERSION="2.0.0"
REACT_APP_SENTRY_DSN="your-frontend-sentry-dsn"

# ==============================================
# إعدادات التطوير / Development Settings
# ==============================================
VITE_API_URL="http://localhost:8000/api/v1"
VITE_WS_URL="ws://localhost:8000/ws"
VITE_APP_NAME="ستوديو الربيع للأتمتة"

# ==============================================
# إعدادات الإنتاج / Production Settings
# ==============================================
# HTTPS_REDIRECT=true
# SECURE_SSL_REDIRECT=true
# SESSION_COOKIE_SECURE=true
# CSRF_COOKIE_SECURE=true

# ==============================================
# إعدادات التخزين السحابي / Cloud Storage Settings
# ==============================================
# AWS_ACCESS_KEY_ID="your-aws-access-key"
# AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
# AWS_STORAGE_BUCKET_NAME="your-s3-bucket"
# AWS_S3_REGION_NAME="us-east-1"

# ==============================================
# إعدادات CDN / CDN Settings
# ==============================================
# CDN_URL="https://your-cdn-domain.com"
# STATIC_URL="https://your-cdn-domain.com/static/"
# MEDIA_URL="https://your-cdn-domain.com/media/"

# ==============================================
# ملاحظات مهمة / Important Notes
# ==============================================
# 1. تأكد من تغيير جميع المفاتيح السرية في بيئة الإنتاج
# 2. لا تشارك هذا الملف مع أي شخص
# 3. أضف .env إلى ملف .gitignore
# 4. استخدم أدوات إدارة الأسرار في بيئة الإنتاج
# 5. قم بعمل نسخة احتياطية من المفاتيح بشكل آمن
