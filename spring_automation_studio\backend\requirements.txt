# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database
sqlalchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9
redis==5.0.1

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Background Tasks
celery==5.3.4
flower==2.0.1

# AI Services
openai==1.3.7
google-generativeai==0.3.2
elevenlabs==0.2.26
replicate==0.15.4

# Image Processing
Pillow==10.1.0
opencv-python==********
moviepy==1.0.3

# Audio Processing
pydub==0.25.1
librosa==0.10.1

# HTTP Requests
httpx==0.25.2
aiohttp==3.9.1
requests==2.31.0

# Utilities
python-dotenv==1.0.0
loguru==0.7.2
tqdm==4.66.1
click==8.1.7

# Translation
googletrans==4.0.0rc1

# File Handling
aiofiles==23.2.1
python-magic==0.4.27

# Validation & Serialization
marshmallow==3.20.1
email-validator==2.1.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Monitoring
prometheus-client==0.19.0
sentry-sdk[fastapi]==1.38.0

# CORS
fastapi-cors==0.0.6

# WebSocket
websockets==12.0

# Caching
aiocache==0.12.2

# File Upload
python-multipart==0.0.6

# Date/Time
python-dateutil==2.8.2
pytz==2023.3

# Configuration
dynaconf==3.2.4

# Rate Limiting
slowapi==0.1.9

# Background Jobs
rq==1.15.1

# Media Processing
ffmpeg-python==0.2.0
imageio==2.33.1
imageio-ffmpeg==0.4.9

# Text Processing
nltk==3.8.1
spacy==3.7.2

# API Documentation
fastapi-users==12.1.2
fastapi-pagination==0.12.13

# Async Support
asyncpg==0.29.0
aioredis==2.0.1

# Monitoring & Logging
structlog==23.2.0
rich==13.7.0

# Security
cryptography==41.0.7
bcrypt==4.1.2

# Environment
python-decouple==3.8
