# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
from .array_json_schema_property_input import ArrayJsonSchemaPropertyInput
from .object_json_schema_property_input import ObjectJsonSchemaPropertyInput
import typing
import pydantic
from .dynamic_variables_config import DynamicVariablesConfig
from .prompt_agent_input import PromptAgentInput
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class AgentConfigApiModelInput(UncheckedBaseModel):
    first_message: typing.Optional[str] = pydantic.Field(default=None)
    """
    If non-empty, the first message the agent will say. If empty, the agent waits for the user to start the discussion.
    """

    language: typing.Optional[str] = pydantic.Field(default=None)
    """
    Language of the agent - used for ASR and TTS
    """

    dynamic_variables: typing.Optional[DynamicVariablesConfig] = pydantic.Field(default=None)
    """
    Configuration for dynamic variables
    """

    prompt: typing.Optional[PromptAgentInput] = pydantic.Field(default=None)
    """
    The prompt for the agent
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
