# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from .conv_ai_workspace_stored_secret_config import ConvAiWorkspaceStoredSecretConfig
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic


class GetWorkspaceSecretsResponseModel(UncheckedBaseModel):
    secrets: typing.List[ConvAiWorkspaceStoredSecretConfig]

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
