# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations
from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic
import typing_extensions
from ..core.unchecked_base_model import UnionMetadata


class ExportOptions_Docx(UncheckedBaseModel):
    format: typing.Literal["docx"] = "docx"
    include_speakers: typing.Optional[bool] = None
    include_timestamps: typing.Optional[bool] = None
    segment_on_silence_longer_than_s: typing.Optional[float] = None
    max_segment_duration_s: typing.Optional[float] = None
    max_segment_chars: typing.Optional[int] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class ExportOptions_Html(UncheckedBaseModel):
    format: typing.Literal["html"] = "html"
    include_speakers: typing.Optional[bool] = None
    include_timestamps: typing.Optional[bool] = None
    segment_on_silence_longer_than_s: typing.Optional[float] = None
    max_segment_duration_s: typing.Optional[float] = None
    max_segment_chars: typing.Optional[int] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class ExportOptions_Pdf(UncheckedBaseModel):
    format: typing.Literal["pdf"] = "pdf"
    include_speakers: typing.Optional[bool] = None
    include_timestamps: typing.Optional[bool] = None
    segment_on_silence_longer_than_s: typing.Optional[float] = None
    max_segment_duration_s: typing.Optional[float] = None
    max_segment_chars: typing.Optional[int] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class ExportOptions_SegmentedJson(UncheckedBaseModel):
    format: typing.Literal["segmented_json"] = "segmented_json"
    segment_on_silence_longer_than_s: typing.Optional[float] = None
    max_segment_duration_s: typing.Optional[float] = None
    max_segment_chars: typing.Optional[int] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class ExportOptions_Srt(UncheckedBaseModel):
    format: typing.Literal["srt"] = "srt"
    max_characters_per_line: typing.Optional[int] = None
    include_speakers: typing.Optional[bool] = None
    include_timestamps: typing.Optional[bool] = None
    segment_on_silence_longer_than_s: typing.Optional[float] = None
    max_segment_duration_s: typing.Optional[float] = None
    max_segment_chars: typing.Optional[int] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class ExportOptions_Txt(UncheckedBaseModel):
    format: typing.Literal["txt"] = "txt"
    max_characters_per_line: typing.Optional[int] = None
    include_speakers: typing.Optional[bool] = None
    include_timestamps: typing.Optional[bool] = None
    segment_on_silence_longer_than_s: typing.Optional[float] = None
    max_segment_duration_s: typing.Optional[float] = None
    max_segment_chars: typing.Optional[int] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


ExportOptions = typing_extensions.Annotated[
    typing.Union[
        ExportOptions_Docx,
        ExportOptions_Html,
        ExportOptions_Pdf,
        ExportOptions_SegmentedJson,
        ExportOptions_Srt,
        ExportOptions_Txt,
    ],
    UnionMetadata(discriminant="format"),
]
