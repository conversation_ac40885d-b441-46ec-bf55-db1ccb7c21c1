# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations
from ..core.unchecked_base_model import UncheckedBaseModel
from .array_json_schema_property_input import ArrayJsonSchemaPropertyInput
from .object_json_schema_property_input import ObjectJsonSchemaPropertyInput
import typing
from .dynamic_variables_config import DynamicVariablesConfig
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic
from .webhook_tool_api_schema_config_input import WebhookToolApiSchemaConfigInput
import typing_extensions
from ..core.unchecked_base_model import UnionMetadata


class PromptAgentInputToolsItem_Client(UncheckedBaseModel):
    """
    The type of tool
    """

    type: typing.Literal["client"] = "client"
    name: str
    description: str
    parameters: typing.Optional[ObjectJsonSchemaPropertyInput] = None
    expects_response: typing.Optional[bool] = None
    response_timeout_secs: typing.Optional[int] = None
    dynamic_variables: typing.Optional[DynamicVariablesConfig] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class PromptAgentInputToolsItem_System(UncheckedBaseModel):
    """
    The type of tool
    """

    type: typing.Literal["system"] = "system"
    name: str
    description: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class PromptAgentInputToolsItem_Webhook(UncheckedBaseModel):
    """
    The type of tool
    """

    type: typing.Literal["webhook"] = "webhook"
    name: str
    description: str
    api_schema: WebhookToolApiSchemaConfigInput
    dynamic_variables: typing.Optional[DynamicVariablesConfig] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


PromptAgentInputToolsItem = typing_extensions.Annotated[
    typing.Union[
        PromptAgentInputToolsItem_Client,
        PromptAgentInputToolsItem_System,
        PromptAgentInputToolsItem_Webhook,
    ],
    UnionMetadata(discriminant="type"),
]
