"""
ستوديو الربيع للأتمتة - النظام الخلفي الرئيسي
Spring Automation Studio - Main Backend Application
"""

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager
import time
import logging
from pathlib import Path

from app.core.config import settings
from app.core.database import engine, Base
from app.core.redis import redis_client
from app.api.v1.router import api_router
from app.core.exceptions import setup_exception_handlers
from app.core.logging import setup_logging

# إعداد نظام السجلات
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """إدارة دورة حياة التطبيق"""
    # بدء التشغيل
    logger.info("🌸 بدء تشغيل ستوديو الربيع للأتمتة...")
    
    # إنشاء جداول قاعدة البيانات
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # اختبار الاتصال بـ Redis
    try:
        await redis_client.ping()
        logger.info("✅ تم الاتصال بـ Redis بنجاح")
    except Exception as e:
        logger.error(f"❌ فشل الاتصال بـ Redis: {e}")
    
    # إنشاء المجلدات المطلوبة
    Path("uploads").mkdir(exist_ok=True)
    Path("outputs").mkdir(exist_ok=True)
    Path("temp").mkdir(exist_ok=True)
    
    logger.info("🚀 تم تشغيل النظام بنجاح!")
    
    yield
    
    # إغلاق التطبيق
    logger.info("🔄 إغلاق النظام...")
    await redis_client.close()
    await engine.dispose()
    logger.info("👋 تم إغلاق النظام بنجاح")


# إنشاء تطبيق FastAPI
app = FastAPI(
    title="ستوديو الربيع للأتمتة",
    description="نظام متطور لإنتاج المحتوى التعليمي بالذكاء الاصطناعي",
    version="2.0.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan,
    openapi_tags=[
        {
            "name": "authentication",
            "description": "عمليات المصادقة وإدارة المستخدمين"
        },
        {
            "name": "projects",
            "description": "إدارة المشاريع والفيديوهات"
        },
        {
            "name": "ai-services",
            "description": "خدمات الذكاء الاصطناعي"
        },
        {
            "name": "media",
            "description": "إدارة الوسائط والملفات"
        },
        {
            "name": "templates",
            "description": "إدارة القوالب والتصاميم"
        },
        {
            "name": "analytics",
            "description": "التحليلات والإحصائيات"
        }
    ]
)

# إعداد CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# إعداد الأمان
if not settings.DEBUG:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=settings.ALLOWED_HOSTS
    )


# Middleware لقياس وقت الاستجابة
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


# إعداد معالجات الأخطاء
setup_exception_handlers(app)

# تضمين المسارات
app.include_router(api_router, prefix="/api/v1")

# خدمة الملفات الثابتة
app.mount("/static", StaticFiles(directory="static"), name="static")
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")
app.mount("/outputs", StaticFiles(directory="outputs"), name="outputs")


@app.get("/")
async def root():
    """الصفحة الرئيسية"""
    return {
        "message": "🌸 مرحباً بك في ستوديو الربيع للأتمتة",
        "version": "2.0.0",
        "status": "running",
        "docs": "/docs",
        "features": [
            "إنتاج فيديوهات احترافية",
            "ذكاء اصطناعي متطور",
            "دعم كامل للعربية",
            "واجهة سهلة الاستخدام",
            "قوالب متعددة"
        ]
    }


@app.get("/health")
async def health_check():
    """فحص صحة النظام"""
    try:
        # فحص قاعدة البيانات
        async with engine.begin() as conn:
            await conn.execute("SELECT 1")
        db_status = "healthy"
    except Exception as e:
        db_status = f"error: {str(e)}"
    
    try:
        # فحص Redis
        await redis_client.ping()
        redis_status = "healthy"
    except Exception as e:
        redis_status = f"error: {str(e)}"
    
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "services": {
            "database": db_status,
            "redis": redis_status,
            "api": "healthy"
        }
    }


@app.get("/info")
async def system_info():
    """معلومات النظام"""
    return {
        "name": "ستوديو الربيع للأتمتة",
        "version": "2.0.0",
        "description": "نظام متطور لإنتاج المحتوى التعليمي بالذكاء الاصطناعي",
        "features": {
            "ai_text_generation": "توليد النصوص بالذكاء الاصطناعي",
            "voice_synthesis": "تحويل النص إلى كلام طبيعي",
            "image_generation": "توليد الصور بالذكاء الاصطناعي",
            "video_creation": "إنتاج فيديوهات احترافية",
            "arabic_support": "دعم كامل للغة العربية",
            "templates": "قوالب متعددة للفيديوهات",
            "user_management": "نظام إدارة المستخدمين",
            "project_management": "إدارة المشاريع والملفات",
            "analytics": "تحليلات وإحصائيات مفصلة"
        },
        "technologies": {
            "backend": "FastAPI + Python",
            "frontend": "React + TypeScript",
            "database": "PostgreSQL + Redis",
            "ai_services": ["OpenAI", "Google Gemini", "ElevenLabs", "DALL-E"]
        },
        "contact": {
            "email": "<EMAIL>",
            "website": "https://spring-studio.com"
        }
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info"
    )
