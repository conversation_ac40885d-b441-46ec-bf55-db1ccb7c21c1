# This file was auto-generated by <PERSON>rn from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from .literal_json_schema_property import LiteralJsonSchemaProperty
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic


class DataCollectionResultCommonModel(UncheckedBaseModel):
    data_collection_id: str
    value: typing.Optional[typing.Optional[typing.Any]] = None
    json_schema: typing.Optional[LiteralJsonSchemaProperty] = None
    rationale: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
