# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
from .knowledge_base_document_metadata_response_model import (
    KnowledgeBaseDocumentMetadataResponseModel,
)
from .resource_access_info import ResourceAccessInfo
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import typing
import pydantic


class GetKnowledgeBaseUrlResponseModel(UncheckedBaseModel):
    id: str
    name: str
    metadata: KnowledgeBaseDocumentMetadataResponseModel
    prompt_injectable: bool
    access_info: ResourceAccessInfo
    extracted_inner_html: str
    url: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
