# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from ..core.client_wrapper import SyncClientWrapper
from .types.pronunciation_dictionary_add_from_file_request_workspace_access import (
    PronunciationDictionaryAddFromFileRequestWorkspaceAccess,
)
from ..core.request_options import RequestOptions
from ..types.add_pronunciation_dictionary_response_model import (
    AddPronunciationDictionaryResponseModel,
)
from ..core.unchecked_base_model import construct_type
from ..errors.unprocessable_entity_error import UnprocessableEntityError
from ..types.http_validation_error import HttpValidationError
from json.decoder import JSONDecodeError
from ..core.api_error import ApiError
from .types.body_add_a_pronunciation_dictionary_v_1_pronunciation_dictionaries_add_from_rules_post_rules_item import (
    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem,
)
from .types.body_add_a_pronunciation_dictionary_v_1_pronunciation_dictionaries_add_from_rules_post_workspace_access import (
    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostWorkspaceAccess,
)
from ..core.serialization import convert_and_respect_annotation_metadata
from .types.pronunciation_dictionary_rule import PronunciationDictionaryRule
from ..types.add_pronunciation_dictionary_rules_response_model import (
    AddPronunciationDictionaryRulesResponseModel,
)
from ..core.jsonable_encoder import jsonable_encoder
from ..types.remove_pronunciation_dictionary_rules_response_model import (
    RemovePronunciationDictionaryRulesResponseModel,
)
from ..types.get_pronunciation_dictionary_metadata_response import (
    GetPronunciationDictionaryMetadataResponse,
)
from .types.pronunciation_dictionary_get_all_request_sort import (
    PronunciationDictionaryGetAllRequestSort,
)
from ..types.get_pronunciation_dictionaries_metadata_response_model import (
    GetPronunciationDictionariesMetadataResponseModel,
)
from ..core.client_wrapper import AsyncClientWrapper

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class PronunciationDictionaryClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def add_from_file(
        self,
        *,
        name: str,
        file: typing.Optional[str] = OMIT,
        description: typing.Optional[str] = OMIT,
        workspace_access: typing.Optional[PronunciationDictionaryAddFromFileRequestWorkspaceAccess] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddPronunciationDictionaryResponseModel:
        """
        Creates a new pronunciation dictionary from a lexicon .PLS file

        Parameters
        ----------
        name : str
            The name of the pronunciation dictionary, used for identification only.

        file : typing.Optional[str]
            A lexicon .pls file which we will use to initialize the project with.

        description : typing.Optional[str]
            A description of the pronunciation dictionary, used for identification only.

        workspace_access : typing.Optional[PronunciationDictionaryAddFromFileRequestWorkspaceAccess]
            Should be one of 'admin', 'editor' or 'viewer'. If not provided, defaults to no access.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddPronunciationDictionaryResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.pronunciation_dictionary.add_from_file(
            name="name",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/pronunciation-dictionaries/add-from-file",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            data={
                "name": name,
                "file": file,
                "description": description,
                "workspace_access": workspace_access,
            },
            files={},
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    AddPronunciationDictionaryResponseModel,
                    construct_type(
                        type_=AddPronunciationDictionaryResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def add_from_rules(
        self,
        *,
        rules: typing.Sequence[BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem],
        name: str,
        description: typing.Optional[str] = OMIT,
        workspace_access: typing.Optional[
            BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostWorkspaceAccess
        ] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddPronunciationDictionaryResponseModel:
        """
        Creates a new pronunciation dictionary from provided rules.

        Parameters
        ----------
        rules : typing.Sequence[BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem]
            List of pronunciation rules. Rule can be either:
                an alias rule: {'string_to_replace': 'a', 'type': 'alias', 'alias': 'b', }
                or a phoneme rule: {'string_to_replace': 'a', 'type': 'phoneme', 'phoneme': 'b', 'alphabet': 'ipa' }

        name : str
            The name of the pronunciation dictionary, used for identification only.

        description : typing.Optional[str]
            A description of the pronunciation dictionary, used for identification only.

        workspace_access : typing.Optional[BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostWorkspaceAccess]
            Should be one of 'admin', 'editor' or 'viewer'. If not provided, defaults to no access.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddPronunciationDictionaryResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs
        from elevenlabs.pronunciation_dictionary import (
            BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Alias,
        )

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.pronunciation_dictionary.add_from_rules(
            rules=[
                BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Alias(
                    string_to_replace="Thailand",
                    alias="tie-land",
                )
            ],
            name="My Dictionary",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/pronunciation-dictionaries/add-from-rules",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "rules": convert_and_respect_annotation_metadata(
                    object_=rules,
                    annotation=typing.Sequence[
                        BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem
                    ],
                    direction="write",
                ),
                "name": name,
                "description": description,
                "workspace_access": workspace_access,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    AddPronunciationDictionaryResponseModel,
                    construct_type(
                        type_=AddPronunciationDictionaryResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def add_rules(
        self,
        pronunciation_dictionary_id: str,
        *,
        rules: typing.Sequence[PronunciationDictionaryRule],
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddPronunciationDictionaryRulesResponseModel:
        """
        Add rules to the pronunciation dictionary

        Parameters
        ----------
        pronunciation_dictionary_id : str
            The id of the pronunciation dictionary

        rules : typing.Sequence[PronunciationDictionaryRule]
            List of pronunciation rules. Rule can be either:
                an alias rule: {'string_to_replace': 'a', 'type': 'alias', 'alias': 'b', }
                or a phoneme rule: {'string_to_replace': 'a', 'type': 'phoneme', 'phoneme': 'b', 'alphabet': 'ipa' }

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddPronunciationDictionaryRulesResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs
        from elevenlabs.pronunciation_dictionary import (
            PronunciationDictionaryRule_Alias,
        )

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.pronunciation_dictionary.add_rules(
            pronunciation_dictionary_id="21m00Tcm4TlvDq8ikWAM",
            rules=[
                PronunciationDictionaryRule_Alias(
                    string_to_replace="Thailand",
                    alias="tie-land",
                )
            ],
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/pronunciation-dictionaries/{jsonable_encoder(pronunciation_dictionary_id)}/add-rules",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "rules": convert_and_respect_annotation_metadata(
                    object_=rules,
                    annotation=typing.Sequence[PronunciationDictionaryRule],
                    direction="write",
                ),
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    AddPronunciationDictionaryRulesResponseModel,
                    construct_type(
                        type_=AddPronunciationDictionaryRulesResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def remove_rules(
        self,
        pronunciation_dictionary_id: str,
        *,
        rule_strings: typing.Sequence[str],
        request_options: typing.Optional[RequestOptions] = None,
    ) -> RemovePronunciationDictionaryRulesResponseModel:
        """
        Remove rules from the pronunciation dictionary

        Parameters
        ----------
        pronunciation_dictionary_id : str
            The id of the pronunciation dictionary

        rule_strings : typing.Sequence[str]
            List of strings to remove from the pronunciation dictionary.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        RemovePronunciationDictionaryRulesResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.pronunciation_dictionary.remove_rules(
            pronunciation_dictionary_id="21m00Tcm4TlvDq8ikWAM",
            rule_strings=["rule_strings"],
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/pronunciation-dictionaries/{jsonable_encoder(pronunciation_dictionary_id)}/remove-rules",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "rule_strings": rule_strings,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    RemovePronunciationDictionaryRulesResponseModel,
                    construct_type(
                        type_=RemovePronunciationDictionaryRulesResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def download(
        self,
        dictionary_id: str,
        version_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Iterator[bytes]:
        """
        Get a PLS file with a pronunciation dictionary version rules

        Parameters
        ----------
        dictionary_id : str
            The id of the pronunciation dictionary

        version_id : str
            The id of the version of the pronunciation dictionary

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Yields
        ------
        typing.Iterator[bytes]
            The PLS file containing pronunciation dictionary rules
        """
        with self._client_wrapper.httpx_client.stream(
            f"v1/pronunciation-dictionaries/{jsonable_encoder(dictionary_id)}/{jsonable_encoder(version_id)}/download",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        ) as _response:
            try:
                if 200 <= _response.status_code < 300:
                    _chunk_size = request_options.get("chunk_size", 1024) if request_options is not None else 1024
                    for _chunk in _response.iter_bytes(chunk_size=_chunk_size):
                        yield _chunk
                    return
                _response.read()
                if _response.status_code == 422:
                    raise UnprocessableEntityError(
                        typing.cast(
                            HttpValidationError,
                            construct_type(
                                type_=HttpValidationError,  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                _response_json = _response.json()
            except JSONDecodeError:
                raise ApiError(status_code=_response.status_code, body=_response.text)
            raise ApiError(status_code=_response.status_code, body=_response_json)

    def get(
        self,
        pronunciation_dictionary_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetPronunciationDictionaryMetadataResponse:
        """
        Get metadata for a pronunciation dictionary

        Parameters
        ----------
        pronunciation_dictionary_id : str
            The id of the pronunciation dictionary

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetPronunciationDictionaryMetadataResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.pronunciation_dictionary.get(
            pronunciation_dictionary_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/pronunciation-dictionaries/{jsonable_encoder(pronunciation_dictionary_id)}/",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetPronunciationDictionaryMetadataResponse,
                    construct_type(
                        type_=GetPronunciationDictionaryMetadataResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_all(
        self,
        *,
        cursor: typing.Optional[str] = None,
        page_size: typing.Optional[int] = None,
        sort: typing.Optional[PronunciationDictionaryGetAllRequestSort] = None,
        sort_direction: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetPronunciationDictionariesMetadataResponseModel:
        """
        Get a list of the pronunciation dictionaries you have access to and their metadata

        Parameters
        ----------
        cursor : typing.Optional[str]
            Used for fetching next page. Cursor is returned in the response.

        page_size : typing.Optional[int]
            How many pronunciation dictionaries to return at maximum. Can not exceed 100, defaults to 30.

        sort : typing.Optional[PronunciationDictionaryGetAllRequestSort]
            Which field to sort by, one of 'created_at_unix' or 'name'.

        sort_direction : typing.Optional[str]
            Which direction to sort the voices in. 'ascending' or 'descending'.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetPronunciationDictionariesMetadataResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.pronunciation_dictionary.get_all()
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/pronunciation-dictionaries/",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            params={
                "cursor": cursor,
                "page_size": page_size,
                "sort": sort,
                "sort_direction": sort_direction,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetPronunciationDictionariesMetadataResponseModel,
                    construct_type(
                        type_=GetPronunciationDictionariesMetadataResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)


class AsyncPronunciationDictionaryClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def add_from_file(
        self,
        *,
        name: str,
        file: typing.Optional[str] = OMIT,
        description: typing.Optional[str] = OMIT,
        workspace_access: typing.Optional[PronunciationDictionaryAddFromFileRequestWorkspaceAccess] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddPronunciationDictionaryResponseModel:
        """
        Creates a new pronunciation dictionary from a lexicon .PLS file

        Parameters
        ----------
        name : str
            The name of the pronunciation dictionary, used for identification only.

        file : typing.Optional[str]
            A lexicon .pls file which we will use to initialize the project with.

        description : typing.Optional[str]
            A description of the pronunciation dictionary, used for identification only.

        workspace_access : typing.Optional[PronunciationDictionaryAddFromFileRequestWorkspaceAccess]
            Should be one of 'admin', 'editor' or 'viewer'. If not provided, defaults to no access.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddPronunciationDictionaryResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.pronunciation_dictionary.add_from_file(
                name="name",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/pronunciation-dictionaries/add-from-file",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            data={
                "name": name,
                "file": file,
                "description": description,
                "workspace_access": workspace_access,
            },
            files={},
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    AddPronunciationDictionaryResponseModel,
                    construct_type(
                        type_=AddPronunciationDictionaryResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def add_from_rules(
        self,
        *,
        rules: typing.Sequence[BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem],
        name: str,
        description: typing.Optional[str] = OMIT,
        workspace_access: typing.Optional[
            BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostWorkspaceAccess
        ] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddPronunciationDictionaryResponseModel:
        """
        Creates a new pronunciation dictionary from provided rules.

        Parameters
        ----------
        rules : typing.Sequence[BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem]
            List of pronunciation rules. Rule can be either:
                an alias rule: {'string_to_replace': 'a', 'type': 'alias', 'alias': 'b', }
                or a phoneme rule: {'string_to_replace': 'a', 'type': 'phoneme', 'phoneme': 'b', 'alphabet': 'ipa' }

        name : str
            The name of the pronunciation dictionary, used for identification only.

        description : typing.Optional[str]
            A description of the pronunciation dictionary, used for identification only.

        workspace_access : typing.Optional[BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostWorkspaceAccess]
            Should be one of 'admin', 'editor' or 'viewer'. If not provided, defaults to no access.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddPronunciationDictionaryResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs
        from elevenlabs.pronunciation_dictionary import (
            BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Alias,
        )

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.pronunciation_dictionary.add_from_rules(
                rules=[
                    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Alias(
                        string_to_replace="Thailand",
                        alias="tie-land",
                    )
                ],
                name="My Dictionary",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/pronunciation-dictionaries/add-from-rules",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "rules": convert_and_respect_annotation_metadata(
                    object_=rules,
                    annotation=typing.Sequence[
                        BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem
                    ],
                    direction="write",
                ),
                "name": name,
                "description": description,
                "workspace_access": workspace_access,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    AddPronunciationDictionaryResponseModel,
                    construct_type(
                        type_=AddPronunciationDictionaryResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def add_rules(
        self,
        pronunciation_dictionary_id: str,
        *,
        rules: typing.Sequence[PronunciationDictionaryRule],
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddPronunciationDictionaryRulesResponseModel:
        """
        Add rules to the pronunciation dictionary

        Parameters
        ----------
        pronunciation_dictionary_id : str
            The id of the pronunciation dictionary

        rules : typing.Sequence[PronunciationDictionaryRule]
            List of pronunciation rules. Rule can be either:
                an alias rule: {'string_to_replace': 'a', 'type': 'alias', 'alias': 'b', }
                or a phoneme rule: {'string_to_replace': 'a', 'type': 'phoneme', 'phoneme': 'b', 'alphabet': 'ipa' }

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddPronunciationDictionaryRulesResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs
        from elevenlabs.pronunciation_dictionary import (
            PronunciationDictionaryRule_Alias,
        )

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.pronunciation_dictionary.add_rules(
                pronunciation_dictionary_id="21m00Tcm4TlvDq8ikWAM",
                rules=[
                    PronunciationDictionaryRule_Alias(
                        string_to_replace="Thailand",
                        alias="tie-land",
                    )
                ],
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/pronunciation-dictionaries/{jsonable_encoder(pronunciation_dictionary_id)}/add-rules",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "rules": convert_and_respect_annotation_metadata(
                    object_=rules,
                    annotation=typing.Sequence[PronunciationDictionaryRule],
                    direction="write",
                ),
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    AddPronunciationDictionaryRulesResponseModel,
                    construct_type(
                        type_=AddPronunciationDictionaryRulesResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def remove_rules(
        self,
        pronunciation_dictionary_id: str,
        *,
        rule_strings: typing.Sequence[str],
        request_options: typing.Optional[RequestOptions] = None,
    ) -> RemovePronunciationDictionaryRulesResponseModel:
        """
        Remove rules from the pronunciation dictionary

        Parameters
        ----------
        pronunciation_dictionary_id : str
            The id of the pronunciation dictionary

        rule_strings : typing.Sequence[str]
            List of strings to remove from the pronunciation dictionary.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        RemovePronunciationDictionaryRulesResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.pronunciation_dictionary.remove_rules(
                pronunciation_dictionary_id="21m00Tcm4TlvDq8ikWAM",
                rule_strings=["rule_strings"],
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/pronunciation-dictionaries/{jsonable_encoder(pronunciation_dictionary_id)}/remove-rules",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "rule_strings": rule_strings,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    RemovePronunciationDictionaryRulesResponseModel,
                    construct_type(
                        type_=RemovePronunciationDictionaryRulesResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def download(
        self,
        dictionary_id: str,
        version_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.AsyncIterator[bytes]:
        """
        Get a PLS file with a pronunciation dictionary version rules

        Parameters
        ----------
        dictionary_id : str
            The id of the pronunciation dictionary

        version_id : str
            The id of the version of the pronunciation dictionary

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Yields
        ------
        typing.AsyncIterator[bytes]
            The PLS file containing pronunciation dictionary rules
        """
        async with self._client_wrapper.httpx_client.stream(
            f"v1/pronunciation-dictionaries/{jsonable_encoder(dictionary_id)}/{jsonable_encoder(version_id)}/download",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        ) as _response:
            try:
                if 200 <= _response.status_code < 300:
                    _chunk_size = request_options.get("chunk_size", 1024) if request_options is not None else 1024
                    async for _chunk in _response.aiter_bytes(chunk_size=_chunk_size):
                        yield _chunk
                    return
                await _response.aread()
                if _response.status_code == 422:
                    raise UnprocessableEntityError(
                        typing.cast(
                            HttpValidationError,
                            construct_type(
                                type_=HttpValidationError,  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                _response_json = _response.json()
            except JSONDecodeError:
                raise ApiError(status_code=_response.status_code, body=_response.text)
            raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get(
        self,
        pronunciation_dictionary_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetPronunciationDictionaryMetadataResponse:
        """
        Get metadata for a pronunciation dictionary

        Parameters
        ----------
        pronunciation_dictionary_id : str
            The id of the pronunciation dictionary

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetPronunciationDictionaryMetadataResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.pronunciation_dictionary.get(
                pronunciation_dictionary_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/pronunciation-dictionaries/{jsonable_encoder(pronunciation_dictionary_id)}/",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetPronunciationDictionaryMetadataResponse,
                    construct_type(
                        type_=GetPronunciationDictionaryMetadataResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_all(
        self,
        *,
        cursor: typing.Optional[str] = None,
        page_size: typing.Optional[int] = None,
        sort: typing.Optional[PronunciationDictionaryGetAllRequestSort] = None,
        sort_direction: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetPronunciationDictionariesMetadataResponseModel:
        """
        Get a list of the pronunciation dictionaries you have access to and their metadata

        Parameters
        ----------
        cursor : typing.Optional[str]
            Used for fetching next page. Cursor is returned in the response.

        page_size : typing.Optional[int]
            How many pronunciation dictionaries to return at maximum. Can not exceed 100, defaults to 30.

        sort : typing.Optional[PronunciationDictionaryGetAllRequestSort]
            Which field to sort by, one of 'created_at_unix' or 'name'.

        sort_direction : typing.Optional[str]
            Which direction to sort the voices in. 'ascending' or 'descending'.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetPronunciationDictionariesMetadataResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.pronunciation_dictionary.get_all()


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/pronunciation-dictionaries/",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            params={
                "cursor": cursor,
                "page_size": page_size,
                "sort": sort,
                "sort_direction": sort_direction,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetPronunciationDictionariesMetadataResponseModel,
                    construct_type(
                        type_=GetPronunciationDictionariesMetadataResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)
