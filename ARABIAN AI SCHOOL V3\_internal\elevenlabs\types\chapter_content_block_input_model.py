# This file was auto-generated by <PERSON>rn from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from .chapter_content_paragraph_tts_node_input_model import (
    ChapterContentParagraphTtsNodeInputModel,
)
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic


class ChapterContentBlockInputModel(UncheckedBaseModel):
    block_id: typing.Optional[str] = None
    nodes: typing.List[ChapterContentParagraphTtsNodeInputModel]

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
