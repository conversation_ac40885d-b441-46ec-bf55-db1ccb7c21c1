"""
نموذج المشروع
Project Model
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Enum, ForeignKey, JSON, Float
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from datetime import datetime
from typing import Optional, Dict, Any

from app.core.database import Base


class ProjectStatus(str, enum.Enum):
    """حالات المشروع"""
    DRAFT = "draft"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ProjectType(str, enum.Enum):
    """أنواع المشاريع"""
    EDUCATIONAL = "educational"
    MARKETING = "marketing"
    ENTERTAINMENT = "entertainment"
    NEWS = "news"
    TUTORIAL = "tutorial"
    PRESENTATION = "presentation"


class VideoQuality(str, enum.Enum):
    """جودة الفيديو"""
    HD_720 = "720p"
    FULL_HD_1080 = "1080p"
    UHD_4K = "4k"


class AspectRatio(str, enum.Enum):
    """نسبة العرض إلى الارتفاع"""
    SQUARE = "1:1"
    LANDSCAPE = "16:9"
    PORTRAIT = "9:16"
    WIDE = "21:9"


class Project(Base):
    """نموذج المشروع"""
    __tablename__ = "projects"
    
    # المعرف الفريد
    id = Column(Integer, primary_key=True, index=True)
    
    # معلومات أساسية
    title = Column(String(200), nullable=False, index=True)
    description = Column(Text, nullable=True)
    topic = Column(String(500), nullable=False)
    
    # نوع وحالة المشروع
    project_type = Column(Enum(ProjectType), default=ProjectType.EDUCATIONAL, nullable=False)
    status = Column(Enum(ProjectStatus), default=ProjectStatus.DRAFT, nullable=False)
    
    # المالك
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # إعدادات الفيديو
    video_quality = Column(Enum(VideoQuality), default=VideoQuality.FULL_HD_1080, nullable=False)
    aspect_ratio = Column(Enum(AspectRatio), default=AspectRatio.PORTRAIT, nullable=False)
    duration_seconds = Column(Integer, nullable=True)
    fps = Column(Integer, default=30, nullable=False)
    
    # إعدادات الصوت
    voice_id = Column(String(50), nullable=True)
    voice_speed = Column(Float, default=1.0, nullable=False)
    voice_style = Column(String(20), default="natural", nullable=False)
    
    # إعدادات التصميم
    template_id = Column(Integer, ForeignKey("templates.id"), nullable=True)
    color_scheme = Column(String(20), default="blue", nullable=False)
    font_family = Column(String(50), default="Arial", nullable=False)
    font_size = Column(Integer, default=24, nullable=False)
    
    # المحتوى المولد
    generated_text = Column(Text, nullable=True)
    generated_script = Column(JSON, nullable=True)  # قائمة الجمل مع التوقيتات
    
    # ملفات الإخراج
    output_video_url = Column(String(500), nullable=True)
    output_audio_url = Column(String(500), nullable=True)
    thumbnail_url = Column(String(500), nullable=True)
    
    # إحصائيات
    view_count = Column(Integer, default=0, nullable=False)
    download_count = Column(Integer, default=0, nullable=False)
    share_count = Column(Integer, default=0, nullable=False)
    
    # معلومات المعالجة
    processing_started_at = Column(DateTime(timezone=True), nullable=True)
    processing_completed_at = Column(DateTime(timezone=True), nullable=True)
    processing_duration_seconds = Column(Integer, nullable=True)
    error_message = Column(Text, nullable=True)
    
    # إعدادات متقدمة
    settings = Column(JSON, nullable=True)  # إعدادات إضافية مخصصة
    metadata = Column(JSON, nullable=True)  # معلومات إضافية
    
    # حالة النشر
    is_public = Column(Boolean, default=False, nullable=False)
    is_featured = Column(Boolean, default=False, nullable=False)
    is_archived = Column(Boolean, default=False, nullable=False)
    
    # تواريخ مهمة
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), nullable=True)
    published_at = Column(DateTime(timezone=True), nullable=True)
    
    # العلاقات
    owner = relationship("User", back_populates="projects")
    template = relationship("Template", back_populates="projects")
    media_files = relationship("ProjectMedia", back_populates="project", cascade="all, delete-orphan")
    analytics = relationship("ProjectAnalytics", back_populates="project", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Project(id={self.id}, title='{self.title}', status='{self.status}')>"
    
    @property
    def is_completed(self) -> bool:
        """التحقق من اكتمال المشروع"""
        return self.status == ProjectStatus.COMPLETED
    
    @property
    def is_processing(self) -> bool:
        """التحقق من كون المشروع قيد المعالجة"""
        return self.status == ProjectStatus.PROCESSING
    
    @property
    def has_output(self) -> bool:
        """التحقق من وجود ملف إخراج"""
        return self.output_video_url is not None
    
    def start_processing(self):
        """بدء معالجة المشروع"""
        self.status = ProjectStatus.PROCESSING
        self.processing_started_at = datetime.utcnow()
        self.error_message = None
    
    def complete_processing(self, output_video_url: str, output_audio_url: str = None, thumbnail_url: str = None):
        """إكمال معالجة المشروع"""
        self.status = ProjectStatus.COMPLETED
        self.processing_completed_at = datetime.utcnow()
        self.output_video_url = output_video_url
        self.output_audio_url = output_audio_url
        self.thumbnail_url = thumbnail_url
        
        if self.processing_started_at:
            duration = datetime.utcnow() - self.processing_started_at
            self.processing_duration_seconds = int(duration.total_seconds())
    
    def fail_processing(self, error_message: str):
        """فشل معالجة المشروع"""
        self.status = ProjectStatus.FAILED
        self.error_message = error_message
        self.processing_completed_at = datetime.utcnow()
    
    def cancel_processing(self):
        """إلغاء معالجة المشروع"""
        self.status = ProjectStatus.CANCELLED
        self.processing_completed_at = datetime.utcnow()
    
    def publish(self):
        """نشر المشروع"""
        if self.is_completed:
            self.is_public = True
            self.published_at = datetime.utcnow()
    
    def unpublish(self):
        """إلغاء نشر المشروع"""
        self.is_public = False
        self.published_at = None
    
    def archive(self):
        """أرشفة المشروع"""
        self.is_archived = True
        self.is_public = False
    
    def unarchive(self):
        """إلغاء أرشفة المشروع"""
        self.is_archived = False
    
    def increment_view_count(self):
        """زيادة عدد المشاهدات"""
        self.view_count += 1
    
    def increment_download_count(self):
        """زيادة عدد التحميلات"""
        self.download_count += 1
    
    def increment_share_count(self):
        """زيادة عدد المشاركات"""
        self.share_count += 1
    
    def to_dict(self, include_content: bool = True) -> dict:
        """تحويل إلى قاموس"""
        data = {
            "id": self.id,
            "title": self.title,
            "description": self.description,
            "topic": self.topic,
            "project_type": self.project_type.value,
            "status": self.status.value,
            "owner_id": self.owner_id,
            "video_quality": self.video_quality.value,
            "aspect_ratio": self.aspect_ratio.value,
            "duration_seconds": self.duration_seconds,
            "fps": self.fps,
            "voice_id": self.voice_id,
            "voice_speed": self.voice_speed,
            "voice_style": self.voice_style,
            "template_id": self.template_id,
            "color_scheme": self.color_scheme,
            "font_family": self.font_family,
            "font_size": self.font_size,
            "output_video_url": self.output_video_url,
            "output_audio_url": self.output_audio_url,
            "thumbnail_url": self.thumbnail_url,
            "view_count": self.view_count,
            "download_count": self.download_count,
            "share_count": self.share_count,
            "processing_started_at": self.processing_started_at.isoformat() if self.processing_started_at else None,
            "processing_completed_at": self.processing_completed_at.isoformat() if self.processing_completed_at else None,
            "processing_duration_seconds": self.processing_duration_seconds,
            "error_message": self.error_message,
            "settings": self.settings,
            "metadata": self.metadata,
            "is_public": self.is_public,
            "is_featured": self.is_featured,
            "is_archived": self.is_archived,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "published_at": self.published_at.isoformat() if self.published_at else None,
        }
        
        if include_content:
            data.update({
                "generated_text": self.generated_text,
                "generated_script": self.generated_script,
            })
        
        return data


class ProjectMedia(Base):
    """وسائط المشروع"""
    __tablename__ = "project_media"
    
    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False, index=True)
    media_type = Column(String(20), nullable=False)  # image, audio, video
    file_url = Column(String(500), nullable=False)
    file_name = Column(String(255), nullable=False)
    file_size = Column(Integer, nullable=True)
    duration_seconds = Column(Float, nullable=True)
    order_index = Column(Integer, default=0, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # العلاقات
    project = relationship("Project", back_populates="media_files")
    
    def __repr__(self):
        return f"<ProjectMedia(id={self.id}, project_id={self.project_id}, type='{self.media_type}')>"


class ProjectAnalytics(Base):
    """تحليلات المشروع"""
    __tablename__ = "project_analytics"
    
    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False, index=True)
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    views = Column(Integer, default=0, nullable=False)
    downloads = Column(Integer, default=0, nullable=False)
    shares = Column(Integer, default=0, nullable=False)
    unique_visitors = Column(Integer, default=0, nullable=False)
    
    # العلاقات
    project = relationship("Project", back_populates="analytics")
    
    def __repr__(self):
        return f"<ProjectAnalytics(id={self.id}, project_id={self.project_id}, date='{self.date}')>"
