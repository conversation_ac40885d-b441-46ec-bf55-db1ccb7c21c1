<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ستوديو الربيع للأتمتة | Spring Automation Studio</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .main-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border-left: 4px solid #667eea;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .feature-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .api-status {
            background: #e8f5e8;
            color: #2d5a2d;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }

        .error {
            background: #ffe6e6;
            color: #d63031;
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
        }

        .success {
            background: #e8f5e8;
            color: #2d5a2d;
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌸 ستوديو الربيع للأتمتة</h1>
            <p>نظام متطور لإنتاج المحتوى التعليمي بالذكاء الاصطناعي</p>
        </div>

        <div id="api-status" class="api-status">
            جاري التحقق من حالة النظام...
        </div>

        <div class="main-card">
            <h2>إنشاء فيديو تعليمي جديد</h2>
            <form id="video-form">
                <div class="form-group">
                    <label for="topic">موضوع الفيديو:</label>
                    <input type="text" id="topic" name="topic" placeholder="مثال: الذكاء الاصطناعي" required>
                </div>

                <div class="form-group">
                    <label for="style">نوع المحتوى:</label>
                    <select id="style" name="style">
                        <option value="educational">تعليمي</option>
                        <option value="informative">إعلامي</option>
                        <option value="tutorial">شرح تطبيقي</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="duration">المدة المقترحة (بالثواني):</label>
                    <input type="number" id="duration" name="duration" value="30" min="15" max="120">
                </div>

                <button type="submit" class="btn" id="create-btn">
                    إنشاء الفيديو
                </button>
            </form>

            <div id="result" style="display: none;"></div>
        </div>

        <div class="features">
            <div class="feature-card">
                <div class="feature-icon">🤖</div>
                <h3>ذكاء اصطناعي متطور</h3>
                <p>استخدام أحدث تقنيات الذكاء الاصطناعي لإنتاج محتوى عالي الجودة</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">🎬</div>
                <h3>إنتاج فيديوهات احترافية</h3>
                <p>تحويل النصوص إلى فيديوهات تعليمية مع الصوت والصور</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">🌍</div>
                <h3>دعم كامل للعربية</h3>
                <p>واجهة عربية كاملة مع دعم النصوص والأصوات العربية</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <h3>سرعة في الإنتاج</h3>
                <p>إنتاج الفيديوهات في دقائق معدودة بدلاً من ساعات</p>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';

        // Check API status
        async function checkAPIStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (data.status === 'healthy') {
                    document.getElementById('api-status').innerHTML = '✅ النظام يعمل بشكل طبيعي';
                    document.getElementById('api-status').style.background = '#e8f5e8';
                    document.getElementById('api-status').style.color = '#2d5a2d';
                } else {
                    throw new Error('API not healthy');
                }
            } catch (error) {
                document.getElementById('api-status').innerHTML = '❌ خطأ في الاتصال بالخادم';
                document.getElementById('api-status').style.background = '#ffe6e6';
                document.getElementById('api-status').style.color = '#d63031';
            }
        }

        // Handle form submission
        document.getElementById('video-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const data = {
                topic: formData.get('topic'),
                style: formData.get('style'),
                duration: parseInt(formData.get('duration'))
            };

            const createBtn = document.getElementById('create-btn');
            const resultDiv = document.getElementById('result');

            // Show loading state
            createBtn.disabled = true;
            createBtn.innerHTML = 'جاري الإنشاء... <span class="loading"></span>';
            resultDiv.style.display = 'none';

            try {
                // Create video
                const response = await fetch(`${API_BASE}/api/create-video`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ تم بدء إنشاء الفيديو بنجاح!</h3>
                            <p><strong>معرف الفيديو:</strong> ${result.video_id}</p>
                            <p><strong>الحالة:</strong> ${result.status}</p>
                            <p>سيتم إنشاء الفيديو في الخلفية. يمكنك التحقق من الحالة لاحقاً.</p>
                            <button onclick="checkVideoStatus('${result.video_id}')" class="btn" style="margin-top: 10px; width: auto; padding: 10px 20px;">
                                التحقق من الحالة
                            </button>
                        </div>
                    `;
                } else {
                    throw new Error(result.message || 'فشل في إنشاء الفيديو');
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ خطأ في إنشاء الفيديو</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            } finally {
                createBtn.disabled = false;
                createBtn.innerHTML = 'إنشاء الفيديو';
                resultDiv.style.display = 'block';
            }
        });

        // Check video status
        async function checkVideoStatus(videoId) {
            try {
                const response = await fetch(`${API_BASE}/api/video/${videoId}`);
                const result = await response.json();

                if (result.success) {
                    const statusDiv = document.querySelector('.success');
                    if (statusDiv) {
                        statusDiv.innerHTML += `
                            <div style="margin-top: 15px; padding: 10px; background: #f0f0f0; border-radius: 5px;">
                                <h4>تفاصيل الفيديو:</h4>
                                <p><strong>الحالة:</strong> ${result.status}</p>
                                <p><strong>الموضوع:</strong> ${result.data.topic}</p>
                                <p><strong>المدة المقدرة:</strong> ${result.data.script?.duration_estimate || 'غير محدد'} ثانية</p>
                                <p><strong>عدد الكلمات:</strong> ${result.data.script?.word_count || 'غير محدد'}</p>
                            </div>
                        `;
                    }
                } else {
                    alert('لم يتم العثور على الفيديو');
                }
            } catch (error) {
                alert('خطأ في التحقق من حالة الفيديو: ' + error.message);
            }
        }

        // Initialize
        checkAPIStatus();
        setInterval(checkAPIStatus, 30000); // Check every 30 seconds
    </script>
</body>
</html>
