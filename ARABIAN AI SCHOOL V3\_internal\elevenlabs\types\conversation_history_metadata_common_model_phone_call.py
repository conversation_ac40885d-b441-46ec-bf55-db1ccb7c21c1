# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations
from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from .conversation_history_twilio_phone_call_model_direction import (
    ConversationHistoryTwilioPhoneCallModelDirection,
)
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic
import typing_extensions
from ..core.unchecked_base_model import UnionMetadata


class ConversationHistoryMetadataCommonModelPhoneCall_Twilio(UncheckedBaseModel):
    type: typing.Literal["twilio"] = "twilio"
    direction: ConversationHistoryTwilioPhoneCallModelDirection
    phone_number_id: str
    agent_number: str
    external_number: str
    stream_sid: str
    call_sid: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


ConversationHistoryMetadataCommonModelPhoneCall = typing_extensions.Annotated[
    ConversationHistoryMetadataCommonModelPhoneCall_Twilio,
    UnionMetadata(discriminant="type"),
]
