import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { Toaster } from 'react-hot-toast';
import { HelmetProvider } from 'react-helmet-async';
import { I18nextProvider } from 'react-i18next';

// Import stores
import { useAuthStore } from './stores/authStore';

// Import components
import LoadingSpinner from './components/ui/LoadingSpinner';
import ErrorBoundary from './components/ErrorBoundary';
import Layout from './components/Layout';
import ProtectedRoute from './components/ProtectedRoute';

// Import pages (lazy loaded)
const HomePage = React.lazy(() => import('./pages/HomePage'));
const LoginPage = React.lazy(() => import('./pages/auth/LoginPage'));
const RegisterPage = React.lazy(() => import('./pages/auth/RegisterPage'));
const DashboardPage = React.lazy(() => import('./pages/DashboardPage'));
const ProjectsPage = React.lazy(() => import('./pages/ProjectsPage'));
const CreateProjectPage = React.lazy(() => import('./pages/CreateProjectPage'));
const ProjectDetailPage = React.lazy(() => import('./pages/ProjectDetailPage'));
const TemplatesPage = React.lazy(() => import('./pages/TemplatesPage'));
const MediaLibraryPage = React.lazy(() => import('./pages/MediaLibraryPage'));
const SettingsPage = React.lazy(() => import('./pages/SettingsPage'));
const ProfilePage = React.lazy(() => import('./pages/ProfilePage'));
const AnalyticsPage = React.lazy(() => import('./pages/AnalyticsPage'));
const NotFoundPage = React.lazy(() => import('./pages/NotFoundPage'));

// Import i18n
import i18n from './i18n';

// Create React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
    mutations: {
      retry: 1,
    },
  },
});

// Loading component
const PageLoader = () => (
  <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
    <div className="text-center">
      <LoadingSpinner size="lg" />
      <p className="mt-4 text-gray-600 font-medium">جاري التحميل...</p>
    </div>
  </div>
);

// Main App component
function App() {
  const { isAuthenticated, isLoading } = useAuthStore();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return <PageLoader />;
  }

  return (
    <ErrorBoundary>
      <HelmetProvider>
        <I18nextProvider i18n={i18n}>
          <QueryClientProvider client={queryClient}>
            <Router>
              <div className="App" dir="rtl">
                <Routes>
                  {/* Public routes */}
                  <Route
                    path="/"
                    element={
                      <Suspense fallback={<PageLoader />}>
                        {isAuthenticated ? <Navigate to="/dashboard" replace /> : <HomePage />}
                      </Suspense>
                    }
                  />
                  
                  <Route
                    path="/login"
                    element={
                      <Suspense fallback={<PageLoader />}>
                        {isAuthenticated ? <Navigate to="/dashboard" replace /> : <LoginPage />}
                      </Suspense>
                    }
                  />
                  
                  <Route
                    path="/register"
                    element={
                      <Suspense fallback={<PageLoader />}>
                        {isAuthenticated ? <Navigate to="/dashboard" replace /> : <RegisterPage />}
                      </Suspense>
                    }
                  />

                  {/* Protected routes */}
                  <Route
                    path="/dashboard"
                    element={
                      <ProtectedRoute>
                        <Layout>
                          <Suspense fallback={<PageLoader />}>
                            <DashboardPage />
                          </Suspense>
                        </Layout>
                      </ProtectedRoute>
                    }
                  />

                  <Route
                    path="/projects"
                    element={
                      <ProtectedRoute>
                        <Layout>
                          <Suspense fallback={<PageLoader />}>
                            <ProjectsPage />
                          </Suspense>
                        </Layout>
                      </ProtectedRoute>
                    }
                  />

                  <Route
                    path="/projects/create"
                    element={
                      <ProtectedRoute>
                        <Layout>
                          <Suspense fallback={<PageLoader />}>
                            <CreateProjectPage />
                          </Suspense>
                        </Layout>
                      </ProtectedRoute>
                    }
                  />

                  <Route
                    path="/projects/:id"
                    element={
                      <ProtectedRoute>
                        <Layout>
                          <Suspense fallback={<PageLoader />}>
                            <ProjectDetailPage />
                          </Suspense>
                        </Layout>
                      </ProtectedRoute>
                    }
                  />

                  <Route
                    path="/templates"
                    element={
                      <ProtectedRoute>
                        <Layout>
                          <Suspense fallback={<PageLoader />}>
                            <TemplatesPage />
                          </Suspense>
                        </Layout>
                      </ProtectedRoute>
                    }
                  />

                  <Route
                    path="/media"
                    element={
                      <ProtectedRoute>
                        <Layout>
                          <Suspense fallback={<PageLoader />}>
                            <MediaLibraryPage />
                          </Suspense>
                        </Layout>
                      </ProtectedRoute>
                    }
                  />

                  <Route
                    path="/analytics"
                    element={
                      <ProtectedRoute>
                        <Layout>
                          <Suspense fallback={<PageLoader />}>
                            <AnalyticsPage />
                          </Suspense>
                        </Layout>
                      </ProtectedRoute>
                    }
                  />

                  <Route
                    path="/profile"
                    element={
                      <ProtectedRoute>
                        <Layout>
                          <Suspense fallback={<PageLoader />}>
                            <ProfilePage />
                          </Suspense>
                        </Layout>
                      </ProtectedRoute>
                    }
                  />

                  <Route
                    path="/settings"
                    element={
                      <ProtectedRoute>
                        <Layout>
                          <Suspense fallback={<PageLoader />}>
                            <SettingsPage />
                          </Suspense>
                        </Layout>
                      </ProtectedRoute>
                    }
                  />

                  {/* 404 page */}
                  <Route
                    path="*"
                    element={
                      <Suspense fallback={<PageLoader />}>
                        <NotFoundPage />
                      </Suspense>
                    }
                  />
                </Routes>

                {/* Toast notifications */}
                <Toaster
                  position="top-center"
                  reverseOrder={false}
                  gutter={8}
                  containerClassName=""
                  containerStyle={{}}
                  toastOptions={{
                    // Default options for all toasts
                    className: '',
                    duration: 4000,
                    style: {
                      background: '#363636',
                      color: '#fff',
                      fontFamily: 'Cairo, sans-serif',
                      direction: 'rtl',
                    },
                    // Success
                    success: {
                      duration: 3000,
                      style: {
                        background: '#10B981',
                      },
                    },
                    // Error
                    error: {
                      duration: 5000,
                      style: {
                        background: '#EF4444',
                      },
                    },
                  }}
                />
              </div>
            </Router>

            {/* React Query DevTools (only in development) */}
            {process.env.NODE_ENV === 'development' && (
              <ReactQueryDevtools initialIsOpen={false} />
            )}
          </QueryClientProvider>
        </I18nextProvider>
      </HelmetProvider>
    </ErrorBoundary>
  );
}

export default App;
