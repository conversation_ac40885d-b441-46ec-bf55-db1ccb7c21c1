# ستوديو الربيع للأتمتة - Docker Compose
# Spring Automation Studio - Docker Compose Configuration

version: '3.8'

services:
  # قاعدة البيانات PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: spring_studio_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: spring_studio
      POSTGRES_USER: spring_user
      POSTGRES_PASSWORD: spring_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - spring_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U spring_user -d spring_studio"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis للتخزين المؤقت والمهام
  redis:
    image: redis:7-alpine
    container_name: spring_studio_redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass spring_redis_password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - spring_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # النظام الخلفي FastAPI
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: spring_studio_backend
    restart: unless-stopped
    environment:
      - DATABASE_URL=******************************************************/spring_studio
      - REDIS_URL=redis://:spring_redis_password@redis:6379
      - SECRET_KEY=your-super-secret-key-change-this
      - JWT_SECRET_KEY=your-jwt-secret-key-change-this
      - DEBUG=false
      - ENVIRONMENT=production
    volumes:
      - ./uploads:/app/uploads
      - ./outputs:/app/outputs
      - ./temp:/app/temp
      - ./logs:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - spring_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker للمهام غير المتزامنة
  celery_worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: spring_studio_celery_worker
    restart: unless-stopped
    command: celery -A app.core.celery worker --loglevel=info --concurrency=4
    environment:
      - DATABASE_URL=******************************************************/spring_studio
      - REDIS_URL=redis://:spring_redis_password@redis:6379
      - CELERY_BROKER_URL=redis://:spring_redis_password@redis:6379/1
      - CELERY_RESULT_BACKEND=redis://:spring_redis_password@redis:6379/1
      - SECRET_KEY=your-super-secret-key-change-this
      - ENVIRONMENT=production
    volumes:
      - ./uploads:/app/uploads
      - ./outputs:/app/outputs
      - ./temp:/app/temp
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - spring_network

  # Celery Beat للمهام المجدولة
  celery_beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: spring_studio_celery_beat
    restart: unless-stopped
    command: celery -A app.core.celery beat --loglevel=info
    environment:
      - DATABASE_URL=******************************************************/spring_studio
      - REDIS_URL=redis://:spring_redis_password@redis:6379
      - CELERY_BROKER_URL=redis://:spring_redis_password@redis:6379/1
      - CELERY_RESULT_BACKEND=redis://:spring_redis_password@redis:6379/1
      - SECRET_KEY=your-super-secret-key-change-this
      - ENVIRONMENT=production
    volumes:
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - spring_network

  # Flower لمراقبة Celery
  flower:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: spring_studio_flower
    restart: unless-stopped
    command: celery -A app.core.celery flower --port=5555
    environment:
      - CELERY_BROKER_URL=redis://:spring_redis_password@redis:6379/1
      - CELERY_RESULT_BACKEND=redis://:spring_redis_password@redis:6379/1
    ports:
      - "5555:5555"
    depends_on:
      - redis
    networks:
      - spring_network

  # واجهة المستخدم React
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - VITE_API_URL=http://localhost:8000/api/v1
        - VITE_WS_URL=ws://localhost:8000/ws
    container_name: spring_studio_frontend
    restart: unless-stopped
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - spring_network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: spring_studio_nginx
    restart: unless-stopped
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
      - ./uploads:/var/www/uploads
      - ./outputs:/var/www/outputs
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
      - frontend
    networks:
      - spring_network

  # Prometheus للمراقبة
  prometheus:
    image: prom/prometheus:latest
    container_name: spring_studio_prometheus
    restart: unless-stopped
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - spring_network

  # Grafana للوحات المراقبة
  grafana:
    image: grafana/grafana:latest
    container_name: spring_studio_grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./docker/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3001:3000"
    depends_on:
      - prometheus
    networks:
      - spring_network

# الشبكات
networks:
  spring_network:
    driver: bridge
    name: spring_studio_network

# المجلدات المستمرة
volumes:
  postgres_data:
    name: spring_studio_postgres_data
  redis_data:
    name: spring_studio_redis_data
  prometheus_data:
    name: spring_studio_prometheus_data
  grafana_data:
    name: spring_studio_grafana_data
