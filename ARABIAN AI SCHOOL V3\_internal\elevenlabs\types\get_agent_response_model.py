# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import pydantic
from .conversational_config_api_model_output import ConversationalConfigApiModelOutput
from .agent_metadata_response_model import AgentMetadataResponseModel
import typing
from .agent_platform_settings_response_model import AgentPlatformSettingsResponseModel
from .get_phone_number_response_model import GetPhoneNumberResponseModel
from ..core.pydantic_utilities import IS_PYDANTIC_V2, update_forward_refs


class GetAgentResponseModel(UncheckedBaseModel):
    agent_id: str = pydantic.Field()
    """
    The ID of the agent
    """

    name: str = pydantic.Field()
    """
    The name of the agent
    """

    conversation_config: ConversationalConfigApiModelOutput = pydantic.Field()
    """
    The conversation configuration of the agent
    """

    metadata: AgentMetadataResponseModel = pydantic.Field()
    """
    The metadata of the agent
    """

    platform_settings: typing.Optional[AgentPlatformSettingsResponseModel] = pydantic.Field(default=None)
    """
    The platform settings of the agent
    """

    phone_numbers: typing.Optional[typing.List[GetPhoneNumberResponseModel]] = pydantic.Field(default=None)
    """
    The phone numbers of the agent
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


from .array_json_schema_property_output import ArrayJsonSchemaPropertyOutput  # noqa: E402

update_forward_refs(ArrayJsonSchemaPropertyOutput)

if IS_PYDANTIC_V2:
    GetAgentResponseModel.model_rebuild()