# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from ..core.client_wrapper import SyncClientWrapper
from ..core.request_options import RequestOptions
from ..types.conversation_signed_url_response_model import (
    ConversationSignedUrlResponseModel,
)
from ..core.unchecked_base_model import construct_type
from ..errors.unprocessable_entity_error import UnprocessableEntityError
from ..types.http_validation_error import HttpValidationError
from json.decoder import JSONDecodeError
from ..core.api_error import ApiError
from ..types.conversation_initiation_client_data_request_input import (
    ConversationInitiationClientDataRequestInput,
)
from ..types.twilio_outbound_call_response import TwilioOutboundCallResponse
from ..core.serialization import convert_and_respect_annotation_metadata
from ..types.conversational_config_api_model_input import (
    ConversationalConfigApiModelInput,
)
from ..types.agent_platform_settings_request_model import (
    AgentPlatformSettingsRequestModel,
)
from ..types.create_agent_response_model import CreateAgentResponseModel
from ..types.get_agent_response_model import GetAgentResponseModel
from ..core.jsonable_encoder import jsonable_encoder
from ..types.get_agent_embed_response_model import GetAgentEmbedResponseModel
from ..types.get_agent_link_response_model import GetAgentLinkResponseModel
from .. import core
from ..types.post_agent_avatar_response_model import PostAgentAvatarResponseModel
from ..types.get_agents_page_response_model import GetAgentsPageResponseModel
from ..types.evaluation_success_result import EvaluationSuccessResult
from ..types.get_conversations_page_response_model import (
    GetConversationsPageResponseModel,
)
from ..types.get_conversation_response_model import GetConversationResponseModel
from ..types.user_feedback_score import UserFeedbackScore
from .types.conversational_ai_create_phone_number_request_body import (
    ConversationalAiCreatePhoneNumberRequestBody,
)
from ..types.create_phone_number_response_model import CreatePhoneNumberResponseModel
from ..types.get_phone_number_response_model import GetPhoneNumberResponseModel
from ..types.get_knowledge_base_list_response_model import (
    GetKnowledgeBaseListResponseModel,
)
from ..types.add_knowledge_base_response_model import AddKnowledgeBaseResponseModel
from ..types.embedding_model_enum import EmbeddingModelEnum
from ..types.rag_index_response_model import RagIndexResponseModel
from .types.conversational_ai_get_knowledge_base_document_by_id_response import (
    ConversationalAiGetKnowledgeBaseDocumentByIdResponse,
)
from ..types.get_knowledge_base_dependent_agents_response_model import (
    GetKnowledgeBaseDependentAgentsResponseModel,
)
from ..types.knowledge_base_document_chunk_response_model import (
    KnowledgeBaseDocumentChunkResponseModel,
)
from ..types.get_conv_ai_settings_response_model import GetConvAiSettingsResponseModel
from ..types.conversation_initiation_client_data_webhook import (
    ConversationInitiationClientDataWebhook,
)
from ..types.conv_ai_webhooks import ConvAiWebhooks
from ..types.get_workspace_secrets_response_model import (
    GetWorkspaceSecretsResponseModel,
)
from ..types.post_workspace_secret_response_model import (
    PostWorkspaceSecretResponseModel,
)
from ..core.client_wrapper import AsyncClientWrapper

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class ConversationalAiClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def get_signed_url(
        self, *, agent_id: str, request_options: typing.Optional[RequestOptions] = None
    ) -> ConversationSignedUrlResponseModel:
        """
        Get a signed url to start a conversation with an agent with an agent that requires authorization

        Parameters
        ----------
        agent_id : str
            The id of the agent you're taking the action on.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ConversationSignedUrlResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.get_signed_url(
            agent_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/convai/conversation/get_signed_url",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            params={
                "agent_id": agent_id,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    ConversationSignedUrlResponseModel,
                    construct_type(
                        type_=ConversationSignedUrlResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def twilio_outbound_call(
        self,
        *,
        agent_id: str,
        agent_phone_number_id: str,
        to_number: str,
        conversation_initiation_client_data: typing.Optional[ConversationInitiationClientDataRequestInput] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> TwilioOutboundCallResponse:
        """
        Handle an outbound call via Twilio

        Parameters
        ----------
        agent_id : str

        agent_phone_number_id : str

        to_number : str

        conversation_initiation_client_data : typing.Optional[ConversationInitiationClientDataRequestInput]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        TwilioOutboundCallResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.twilio_outbound_call(
            agent_id="agent_id",
            agent_phone_number_id="agent_phone_number_id",
            to_number="to_number",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/convai/twilio/outbound_call",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "agent_id": agent_id,
                "agent_phone_number_id": agent_phone_number_id,
                "to_number": to_number,
                "conversation_initiation_client_data": convert_and_respect_annotation_metadata(
                    object_=conversation_initiation_client_data,
                    annotation=ConversationInitiationClientDataRequestInput,
                    direction="write",
                ),
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    TwilioOutboundCallResponse,
                    construct_type(
                        type_=TwilioOutboundCallResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def create_agent(
        self,
        *,
        conversation_config: ConversationalConfigApiModelInput,
        use_tool_ids: typing.Optional[bool] = None,
        platform_settings: typing.Optional[AgentPlatformSettingsRequestModel] = OMIT,
        name: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> CreateAgentResponseModel:
        """
        Create an agent from a config object

        Parameters
        ----------
        conversation_config : ConversationalConfigApiModelInput
            Conversation configuration for an agent

        use_tool_ids : typing.Optional[bool]
            Use tool ids instead of tools specs from request payload.

        platform_settings : typing.Optional[AgentPlatformSettingsRequestModel]
            Platform settings for the agent are all settings that aren't related to the conversation orchestration and content.

        name : typing.Optional[str]
            A name to make the agent easier to find

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        CreateAgentResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ConversationalConfigApiModelInput, ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.create_agent(
            conversation_config=ConversationalConfigApiModelInput(),
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/convai/agents/create",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            params={
                "use_tool_ids": use_tool_ids,
            },
            json={
                "conversation_config": convert_and_respect_annotation_metadata(
                    object_=conversation_config,
                    annotation=ConversationalConfigApiModelInput,
                    direction="write",
                ),
                "platform_settings": convert_and_respect_annotation_metadata(
                    object_=platform_settings,
                    annotation=AgentPlatformSettingsRequestModel,
                    direction="write",
                ),
                "name": name,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    CreateAgentResponseModel,
                    construct_type(
                        type_=CreateAgentResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_agent(
        self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> GetAgentResponseModel:
        """
        Retrieve config for an agent

        Parameters
        ----------
        agent_id : str
            The id of an agent. This is returned on agent creation.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetAgentResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.get_agent(
            agent_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/agents/{jsonable_encoder(agent_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetAgentResponseModel,
                    construct_type(
                        type_=GetAgentResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def delete_agent(self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> None:
        """
        Delete an agent

        Parameters
        ----------
        agent_id : str
            The id of an agent. This is returned on agent creation.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.delete_agent(
            agent_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/agents/{jsonable_encoder(agent_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def update_agent(
        self,
        agent_id: str,
        *,
        use_tool_ids: typing.Optional[bool] = None,
        conversation_config: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = OMIT,
        platform_settings: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = OMIT,
        name: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetAgentResponseModel:
        """
        Patches an Agent settings

        Parameters
        ----------
        agent_id : str
            The id of an agent. This is returned on agent creation.

        use_tool_ids : typing.Optional[bool]
            Use tool ids instead of tools specs from request payload.

        conversation_config : typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]]
            Conversation configuration for an agent

        platform_settings : typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]]
            Platform settings for the agent are all settings that aren't related to the conversation orchestration and content.

        name : typing.Optional[str]
            A name to make the agent easier to find

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetAgentResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.update_agent(
            agent_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/agents/{jsonable_encoder(agent_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="PATCH",
            params={
                "use_tool_ids": use_tool_ids,
            },
            json={
                "conversation_config": conversation_config,
                "platform_settings": platform_settings,
                "name": name,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetAgentResponseModel,
                    construct_type(
                        type_=GetAgentResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_agent_widget(
        self,
        agent_id: str,
        *,
        conversation_signature: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetAgentEmbedResponseModel:
        """
        Retrieve the widget configuration for an agent

        Parameters
        ----------
        agent_id : str
            The id of an agent. This is returned on agent creation.

        conversation_signature : typing.Optional[str]
            An expiring token that enables a conversation to start. These can be generated for an agent using the /v1/convai/conversation/get_signed_url endpoint

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetAgentEmbedResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.get_agent_widget(
            agent_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/agents/{jsonable_encoder(agent_id)}/widget",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            params={
                "conversation_signature": conversation_signature,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetAgentEmbedResponseModel,
                    construct_type(
                        type_=GetAgentEmbedResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_agent_link(
        self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> GetAgentLinkResponseModel:
        """
        Get the current link used to share the agent with others

        Parameters
        ----------
        agent_id : str
            The id of an agent. This is returned on agent creation.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetAgentLinkResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.get_agent_link(
            agent_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/agents/{jsonable_encoder(agent_id)}/link",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetAgentLinkResponseModel,
                    construct_type(
                        type_=GetAgentLinkResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def post_agent_avatar(
        self,
        agent_id: str,
        *,
        avatar_file: core.File,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> PostAgentAvatarResponseModel:
        """
        Sets the avatar for an agent displayed in the widget

        Parameters
        ----------
        agent_id : str
            The id of an agent. This is returned on agent creation.

        avatar_file : core.File
            See core.File for more documentation

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        PostAgentAvatarResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.post_agent_avatar(
            agent_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/agents/{jsonable_encoder(agent_id)}/avatar",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            data={},
            files={
                "avatar_file": avatar_file,
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    PostAgentAvatarResponseModel,
                    construct_type(
                        type_=PostAgentAvatarResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_agents(
        self,
        *,
        cursor: typing.Optional[str] = None,
        page_size: typing.Optional[int] = None,
        search: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetAgentsPageResponseModel:
        """
        Returns a list of your agents and their metadata.

        Parameters
        ----------
        cursor : typing.Optional[str]
            Used for fetching next page. Cursor is returned in the response.

        page_size : typing.Optional[int]
            How many Agents to return at maximum. Can not exceed 100, defaults to 30.

        search : typing.Optional[str]
            Search by agents name.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetAgentsPageResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.get_agents()
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/convai/agents",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            params={
                "cursor": cursor,
                "page_size": page_size,
                "search": search,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetAgentsPageResponseModel,
                    construct_type(
                        type_=GetAgentsPageResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_conversations(
        self,
        *,
        cursor: typing.Optional[str] = None,
        agent_id: typing.Optional[str] = None,
        call_successful: typing.Optional[EvaluationSuccessResult] = None,
        page_size: typing.Optional[int] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetConversationsPageResponseModel:
        """
        Get all conversations of agents that user owns. With option to restrict to a specific agent.

        Parameters
        ----------
        cursor : typing.Optional[str]
            Used for fetching next page. Cursor is returned in the response.

        agent_id : typing.Optional[str]
            The id of the agent you're taking the action on.

        call_successful : typing.Optional[EvaluationSuccessResult]
            The result of the success evaluation

        page_size : typing.Optional[int]
            How many conversations to return at maximum. Can not exceed 100, defaults to 30.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetConversationsPageResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.get_conversations()
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/convai/conversations",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            params={
                "cursor": cursor,
                "agent_id": agent_id,
                "call_successful": call_successful,
                "page_size": page_size,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetConversationsPageResponseModel,
                    construct_type(
                        type_=GetConversationsPageResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_conversation(
        self,
        conversation_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetConversationResponseModel:
        """
        Get the details of a particular conversation

        Parameters
        ----------
        conversation_id : str
            The id of the conversation you're taking the action on.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetConversationResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.get_conversation(
            conversation_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/conversations/{jsonable_encoder(conversation_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetConversationResponseModel,
                    construct_type(
                        type_=GetConversationResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def delete_conversation(
        self,
        conversation_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Delete a particular conversation

        Parameters
        ----------
        conversation_id : str
            The id of the conversation you're taking the action on.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.delete_conversation(
            conversation_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/conversations/{jsonable_encoder(conversation_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    typing.Optional[typing.Any],
                    construct_type(
                        type_=typing.Optional[typing.Any],  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_conversation_audio(
        self,
        conversation_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Iterator[bytes]:
        """
        Get the audio recording of a particular conversation

        Parameters
        ----------
        conversation_id : str
            The id of the conversation you're taking the action on.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Yields
        ------
        typing.Iterator[bytes]
            Successful Response
        """
        with self._client_wrapper.httpx_client.stream(
            f"v1/convai/conversations/{jsonable_encoder(conversation_id)}/audio",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        ) as _response:
            try:
                if 200 <= _response.status_code < 300:
                    _chunk_size = request_options.get("chunk_size", 1024) if request_options is not None else 1024
                    for _chunk in _response.iter_bytes(chunk_size=_chunk_size):
                        yield _chunk
                    return
                _response.read()
                if _response.status_code == 422:
                    raise UnprocessableEntityError(
                        typing.cast(
                            HttpValidationError,
                            construct_type(
                                type_=HttpValidationError,  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                _response_json = _response.json()
            except JSONDecodeError:
                raise ApiError(status_code=_response.status_code, body=_response.text)
            raise ApiError(status_code=_response.status_code, body=_response_json)

    def post_conversation_feedback(
        self,
        conversation_id: str,
        *,
        feedback: UserFeedbackScore,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Send the feedback for the given conversation

        Parameters
        ----------
        conversation_id : str
            The id of the conversation you're taking the action on.

        feedback : UserFeedbackScore
            Either 'like' or 'dislike' to indicate the feedback for the conversation.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.post_conversation_feedback(
            conversation_id="21m00Tcm4TlvDq8ikWAM",
            feedback="like",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/conversations/{jsonable_encoder(conversation_id)}/feedback",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "feedback": feedback,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    typing.Optional[typing.Any],
                    construct_type(
                        type_=typing.Optional[typing.Any],  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def create_phone_number(
        self,
        *,
        request: ConversationalAiCreatePhoneNumberRequestBody,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> CreatePhoneNumberResponseModel:
        """
        Import Phone Number from provider configuration (Twilio or SIP trunk)

        Parameters
        ----------
        request : ConversationalAiCreatePhoneNumberRequestBody

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        CreatePhoneNumberResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import CreateTwilioPhoneNumberRequest, ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.create_phone_number(
            request=CreateTwilioPhoneNumberRequest(
                phone_number="phone_number",
                label="label",
                sid="sid",
                token="token",
            ),
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/convai/phone-numbers/create",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json=convert_and_respect_annotation_metadata(
                object_=request,
                annotation=ConversationalAiCreatePhoneNumberRequestBody,
                direction="write",
            ),
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    CreatePhoneNumberResponseModel,
                    construct_type(
                        type_=CreatePhoneNumberResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_phone_number(
        self,
        phone_number_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetPhoneNumberResponseModel:
        """
        Retrieve Phone Number details by ID

        Parameters
        ----------
        phone_number_id : str
            The id of an agent. This is returned on agent creation.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetPhoneNumberResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.get_phone_number(
            phone_number_id="TeaqRRdTcIfIu2i7BYfT",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/phone-numbers/{jsonable_encoder(phone_number_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetPhoneNumberResponseModel,
                    construct_type(
                        type_=GetPhoneNumberResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def delete_phone_number(
        self,
        phone_number_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Delete Phone Number by ID

        Parameters
        ----------
        phone_number_id : str
            The id of an agent. This is returned on agent creation.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.delete_phone_number(
            phone_number_id="TeaqRRdTcIfIu2i7BYfT",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/phone-numbers/{jsonable_encoder(phone_number_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    typing.Optional[typing.Any],
                    construct_type(
                        type_=typing.Optional[typing.Any],  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def update_phone_number(
        self,
        phone_number_id: str,
        *,
        agent_id: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetPhoneNumberResponseModel:
        """
        Update Phone Number details by ID

        Parameters
        ----------
        phone_number_id : str
            The id of an agent. This is returned on agent creation.

        agent_id : typing.Optional[str]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetPhoneNumberResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.update_phone_number(
            phone_number_id="TeaqRRdTcIfIu2i7BYfT",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/phone-numbers/{jsonable_encoder(phone_number_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="PATCH",
            json={
                "agent_id": agent_id,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetPhoneNumberResponseModel,
                    construct_type(
                        type_=GetPhoneNumberResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_phone_numbers(
        self, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.List[GetPhoneNumberResponseModel]:
        """
        Retrieve all Phone Numbers

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[GetPhoneNumberResponseModel]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.get_phone_numbers()
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/convai/phone-numbers/",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    typing.List[GetPhoneNumberResponseModel],
                    construct_type(
                        type_=typing.List[GetPhoneNumberResponseModel],  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_knowledge_base_list(
        self,
        *,
        cursor: typing.Optional[str] = None,
        page_size: typing.Optional[int] = None,
        search: typing.Optional[str] = None,
        show_only_owned_documents: typing.Optional[bool] = None,
        use_typesense: typing.Optional[bool] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetKnowledgeBaseListResponseModel:
        """
        Get a list of available knowledge base documents

        Parameters
        ----------
        cursor : typing.Optional[str]
            Used for fetching next page. Cursor is returned in the response.

        page_size : typing.Optional[int]
            How many documents to return at maximum. Can not exceed 100, defaults to 30.

        search : typing.Optional[str]
            If specified, the endpoint returns only such knowledge base documents whose names start with this string.

        show_only_owned_documents : typing.Optional[bool]
            If set to true, the endpoint will return only documents owned by you (and not shared from somebody else).

        use_typesense : typing.Optional[bool]
            If set to true, the endpoint will use typesense DB to search for the documents).

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetKnowledgeBaseListResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.get_knowledge_base_list()
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/convai/knowledge-base",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            params={
                "cursor": cursor,
                "page_size": page_size,
                "search": search,
                "show_only_owned_documents": show_only_owned_documents,
                "use_typesense": use_typesense,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetKnowledgeBaseListResponseModel,
                    construct_type(
                        type_=GetKnowledgeBaseListResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def add_to_knowledge_base(
        self,
        *,
        name: typing.Optional[str] = OMIT,
        url: typing.Optional[str] = OMIT,
        file: typing.Optional[core.File] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddKnowledgeBaseResponseModel:
        """
        Uploads a file or reference a webpage to use as part of the shared knowledge base

        Parameters
        ----------
        name : typing.Optional[str]
            A custom, human-readable name for the document.

        url : typing.Optional[str]
            URL to a page of documentation that the agent will have access to in order to interact with users.

        file : typing.Optional[core.File]
            See core.File for more documentation

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddKnowledgeBaseResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.add_to_knowledge_base()
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/convai/knowledge-base",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            data={
                "name": name,
                "url": url,
            },
            files={
                "file": file,
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    AddKnowledgeBaseResponseModel,
                    construct_type(
                        type_=AddKnowledgeBaseResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def create_knowledge_base_url_document(
        self,
        *,
        url: str,
        name: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddKnowledgeBaseResponseModel:
        """
        Create a knowledge base document generated by scraping the given webpage.

        Parameters
        ----------
        url : str
            URL to a page of documentation that the agent will have access to in order to interact with users.

        name : typing.Optional[str]
            A custom, human-readable name for the document.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddKnowledgeBaseResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.create_knowledge_base_url_document(
            url="url",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/convai/knowledge-base/url",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "url": url,
                "name": name,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    AddKnowledgeBaseResponseModel,
                    construct_type(
                        type_=AddKnowledgeBaseResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def create_knowledge_base_file_document(
        self,
        *,
        file: core.File,
        name: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddKnowledgeBaseResponseModel:
        """
        Create a knowledge base document generated form the uploaded file.

        Parameters
        ----------
        file : core.File
            See core.File for more documentation

        name : typing.Optional[str]
            A custom, human-readable name for the document.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddKnowledgeBaseResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.create_knowledge_base_file_document()
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/convai/knowledge-base/file",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            data={
                "name": name,
            },
            files={
                "file": file,
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    AddKnowledgeBaseResponseModel,
                    construct_type(
                        type_=AddKnowledgeBaseResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def create_knowledge_base_text_document(
        self,
        *,
        text: str,
        name: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddKnowledgeBaseResponseModel:
        """
        Create a knowledge base document containing the provided text.

        Parameters
        ----------
        text : str
            Text content to be added to the knowledge base.

        name : typing.Optional[str]
            A custom, human-readable name for the document.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddKnowledgeBaseResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.create_knowledge_base_text_document(
            text="text",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/convai/knowledge-base/text",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "text": text,
                "name": name,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    AddKnowledgeBaseResponseModel,
                    construct_type(
                        type_=AddKnowledgeBaseResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def rag_index_status(
        self,
        documentation_id: str,
        *,
        model: EmbeddingModelEnum,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> RagIndexResponseModel:
        """
        In case the document is not RAG indexed, it triggers rag indexing task, otherwise it just returns the current status.

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        model : EmbeddingModelEnum

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        RagIndexResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.rag_index_status(
            documentation_id="21m00Tcm4TlvDq8ikWAM",
            model="e5_mistral_7b_instruct",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/knowledge-base/{jsonable_encoder(documentation_id)}/rag-index",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "model": model,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    RagIndexResponseModel,
                    construct_type(
                        type_=RagIndexResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_knowledge_base_document_by_id(
        self,
        documentation_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ConversationalAiGetKnowledgeBaseDocumentByIdResponse:
        """
        Get details about a specific documentation making up the agent's knowledge base

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ConversationalAiGetKnowledgeBaseDocumentByIdResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.get_knowledge_base_document_by_id(
            documentation_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/knowledge-base/{jsonable_encoder(documentation_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    ConversationalAiGetKnowledgeBaseDocumentByIdResponse,
                    construct_type(
                        type_=ConversationalAiGetKnowledgeBaseDocumentByIdResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def delete_knowledge_base_document(
        self,
        documentation_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Delete a document from the knowledge base

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.delete_knowledge_base_document(
            documentation_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/knowledge-base/{jsonable_encoder(documentation_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    typing.Optional[typing.Any],
                    construct_type(
                        type_=typing.Optional[typing.Any],  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_dependent_agents(
        self,
        documentation_id: str,
        *,
        cursor: typing.Optional[str] = None,
        page_size: typing.Optional[int] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetKnowledgeBaseDependentAgentsResponseModel:
        """
        Get a list of agents depending on this knowledge base document

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        cursor : typing.Optional[str]
            Used for fetching next page. Cursor is returned in the response.

        page_size : typing.Optional[int]
            How many documents to return at maximum. Can not exceed 100, defaults to 30.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetKnowledgeBaseDependentAgentsResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.get_dependent_agents(
            documentation_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/knowledge-base/{jsonable_encoder(documentation_id)}/dependent-agents",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            params={
                "cursor": cursor,
                "page_size": page_size,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetKnowledgeBaseDependentAgentsResponseModel,
                    construct_type(
                        type_=GetKnowledgeBaseDependentAgentsResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_knowledge_base_document_content(
        self,
        documentation_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> None:
        """
        Get the entire content of a document from the knowledge base

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.get_knowledge_base_document_content(
            documentation_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/knowledge-base/{jsonable_encoder(documentation_id)}/content",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_knowledge_base_document_part_by_id(
        self,
        documentation_id: str,
        chunk_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> KnowledgeBaseDocumentChunkResponseModel:
        """
        Get details about a specific documentation part used by RAG.

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        chunk_id : str
            The id of a document RAG chunk from the knowledge base.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        KnowledgeBaseDocumentChunkResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.get_knowledge_base_document_part_by_id(
            documentation_id="21m00Tcm4TlvDq8ikWAM",
            chunk_id="chunk_id",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/knowledge-base/{jsonable_encoder(documentation_id)}/chunk/{jsonable_encoder(chunk_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    KnowledgeBaseDocumentChunkResponseModel,
                    construct_type(
                        type_=KnowledgeBaseDocumentChunkResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_settings(
        self, *, request_options: typing.Optional[RequestOptions] = None
    ) -> GetConvAiSettingsResponseModel:
        """
        Retrieve Convai settings for the workspace

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetConvAiSettingsResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.get_settings()
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/convai/settings",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetConvAiSettingsResponseModel,
                    construct_type(
                        type_=GetConvAiSettingsResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def update_settings(
        self,
        *,
        conversation_initiation_client_data_webhook: typing.Optional[ConversationInitiationClientDataWebhook] = OMIT,
        webhooks: typing.Optional[ConvAiWebhooks] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetConvAiSettingsResponseModel:
        """
        Update Convai settings for the workspace

        Parameters
        ----------
        conversation_initiation_client_data_webhook : typing.Optional[ConversationInitiationClientDataWebhook]

        webhooks : typing.Optional[ConvAiWebhooks]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetConvAiSettingsResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.update_settings()
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/convai/settings",
            base_url=self._client_wrapper.get_environment().base,
            method="PATCH",
            json={
                "conversation_initiation_client_data_webhook": convert_and_respect_annotation_metadata(
                    object_=conversation_initiation_client_data_webhook,
                    annotation=ConversationInitiationClientDataWebhook,
                    direction="write",
                ),
                "webhooks": convert_and_respect_annotation_metadata(
                    object_=webhooks, annotation=ConvAiWebhooks, direction="write"
                ),
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetConvAiSettingsResponseModel,
                    construct_type(
                        type_=GetConvAiSettingsResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_secrets(
        self, *, request_options: typing.Optional[RequestOptions] = None
    ) -> GetWorkspaceSecretsResponseModel:
        """
        Get all workspace secrets for the user

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetWorkspaceSecretsResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.get_secrets()
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/convai/secrets",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetWorkspaceSecretsResponseModel,
                    construct_type(
                        type_=GetWorkspaceSecretsResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def create_secret(
        self,
        *,
        name: str,
        value: str,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> PostWorkspaceSecretResponseModel:
        """
        Create a new secret for the workspace

        Parameters
        ----------
        name : str

        value : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        PostWorkspaceSecretResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.create_secret(
            name="name",
            value="value",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/convai/secrets",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "name": name,
                "value": value,
                "type": "new",
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    PostWorkspaceSecretResponseModel,
                    construct_type(
                        type_=PostWorkspaceSecretResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def delete_secret(self, secret_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> None:
        """
        Delete a workspace secret if it's not in use

        Parameters
        ----------
        secret_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.delete_secret(
            secret_id="secret_id",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/secrets/{jsonable_encoder(secret_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)


class AsyncConversationalAiClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def get_signed_url(
        self, *, agent_id: str, request_options: typing.Optional[RequestOptions] = None
    ) -> ConversationSignedUrlResponseModel:
        """
        Get a signed url to start a conversation with an agent with an agent that requires authorization

        Parameters
        ----------
        agent_id : str
            The id of the agent you're taking the action on.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ConversationSignedUrlResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.get_signed_url(
                agent_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/convai/conversation/get_signed_url",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            params={
                "agent_id": agent_id,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    ConversationSignedUrlResponseModel,
                    construct_type(
                        type_=ConversationSignedUrlResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def twilio_outbound_call(
        self,
        *,
        agent_id: str,
        agent_phone_number_id: str,
        to_number: str,
        conversation_initiation_client_data: typing.Optional[ConversationInitiationClientDataRequestInput] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> TwilioOutboundCallResponse:
        """
        Handle an outbound call via Twilio

        Parameters
        ----------
        agent_id : str

        agent_phone_number_id : str

        to_number : str

        conversation_initiation_client_data : typing.Optional[ConversationInitiationClientDataRequestInput]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        TwilioOutboundCallResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.twilio_outbound_call(
                agent_id="agent_id",
                agent_phone_number_id="agent_phone_number_id",
                to_number="to_number",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/convai/twilio/outbound_call",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "agent_id": agent_id,
                "agent_phone_number_id": agent_phone_number_id,
                "to_number": to_number,
                "conversation_initiation_client_data": convert_and_respect_annotation_metadata(
                    object_=conversation_initiation_client_data,
                    annotation=ConversationInitiationClientDataRequestInput,
                    direction="write",
                ),
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    TwilioOutboundCallResponse,
                    construct_type(
                        type_=TwilioOutboundCallResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def create_agent(
        self,
        *,
        conversation_config: ConversationalConfigApiModelInput,
        use_tool_ids: typing.Optional[bool] = None,
        platform_settings: typing.Optional[AgentPlatformSettingsRequestModel] = OMIT,
        name: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> CreateAgentResponseModel:
        """
        Create an agent from a config object

        Parameters
        ----------
        conversation_config : ConversationalConfigApiModelInput
            Conversation configuration for an agent

        use_tool_ids : typing.Optional[bool]
            Use tool ids instead of tools specs from request payload.

        platform_settings : typing.Optional[AgentPlatformSettingsRequestModel]
            Platform settings for the agent are all settings that aren't related to the conversation orchestration and content.

        name : typing.Optional[str]
            A name to make the agent easier to find

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        CreateAgentResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs, ConversationalConfigApiModelInput

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.create_agent(
                conversation_config=ConversationalConfigApiModelInput(),
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/convai/agents/create",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            params={
                "use_tool_ids": use_tool_ids,
            },
            json={
                "conversation_config": convert_and_respect_annotation_metadata(
                    object_=conversation_config,
                    annotation=ConversationalConfigApiModelInput,
                    direction="write",
                ),
                "platform_settings": convert_and_respect_annotation_metadata(
                    object_=platform_settings,
                    annotation=AgentPlatformSettingsRequestModel,
                    direction="write",
                ),
                "name": name,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    CreateAgentResponseModel,
                    construct_type(
                        type_=CreateAgentResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_agent(
        self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> GetAgentResponseModel:
        """
        Retrieve config for an agent

        Parameters
        ----------
        agent_id : str
            The id of an agent. This is returned on agent creation.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetAgentResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.get_agent(
                agent_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/agents/{jsonable_encoder(agent_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetAgentResponseModel,
                    construct_type(
                        type_=GetAgentResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def delete_agent(self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> None:
        """
        Delete an agent

        Parameters
        ----------
        agent_id : str
            The id of an agent. This is returned on agent creation.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.delete_agent(
                agent_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/agents/{jsonable_encoder(agent_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def update_agent(
        self,
        agent_id: str,
        *,
        use_tool_ids: typing.Optional[bool] = None,
        conversation_config: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = OMIT,
        platform_settings: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = OMIT,
        name: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetAgentResponseModel:
        """
        Patches an Agent settings

        Parameters
        ----------
        agent_id : str
            The id of an agent. This is returned on agent creation.

        use_tool_ids : typing.Optional[bool]
            Use tool ids instead of tools specs from request payload.

        conversation_config : typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]]
            Conversation configuration for an agent

        platform_settings : typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]]
            Platform settings for the agent are all settings that aren't related to the conversation orchestration and content.

        name : typing.Optional[str]
            A name to make the agent easier to find

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetAgentResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.update_agent(
                agent_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/agents/{jsonable_encoder(agent_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="PATCH",
            params={
                "use_tool_ids": use_tool_ids,
            },
            json={
                "conversation_config": conversation_config,
                "platform_settings": platform_settings,
                "name": name,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetAgentResponseModel,
                    construct_type(
                        type_=GetAgentResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_agent_widget(
        self,
        agent_id: str,
        *,
        conversation_signature: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetAgentEmbedResponseModel:
        """
        Retrieve the widget configuration for an agent

        Parameters
        ----------
        agent_id : str
            The id of an agent. This is returned on agent creation.

        conversation_signature : typing.Optional[str]
            An expiring token that enables a conversation to start. These can be generated for an agent using the /v1/convai/conversation/get_signed_url endpoint

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetAgentEmbedResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.get_agent_widget(
                agent_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/agents/{jsonable_encoder(agent_id)}/widget",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            params={
                "conversation_signature": conversation_signature,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetAgentEmbedResponseModel,
                    construct_type(
                        type_=GetAgentEmbedResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_agent_link(
        self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> GetAgentLinkResponseModel:
        """
        Get the current link used to share the agent with others

        Parameters
        ----------
        agent_id : str
            The id of an agent. This is returned on agent creation.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetAgentLinkResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.get_agent_link(
                agent_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/agents/{jsonable_encoder(agent_id)}/link",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetAgentLinkResponseModel,
                    construct_type(
                        type_=GetAgentLinkResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def post_agent_avatar(
        self,
        agent_id: str,
        *,
        avatar_file: core.File,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> PostAgentAvatarResponseModel:
        """
        Sets the avatar for an agent displayed in the widget

        Parameters
        ----------
        agent_id : str
            The id of an agent. This is returned on agent creation.

        avatar_file : core.File
            See core.File for more documentation

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        PostAgentAvatarResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.post_agent_avatar(
                agent_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/agents/{jsonable_encoder(agent_id)}/avatar",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            data={},
            files={
                "avatar_file": avatar_file,
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    PostAgentAvatarResponseModel,
                    construct_type(
                        type_=PostAgentAvatarResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_agents(
        self,
        *,
        cursor: typing.Optional[str] = None,
        page_size: typing.Optional[int] = None,
        search: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetAgentsPageResponseModel:
        """
        Returns a list of your agents and their metadata.

        Parameters
        ----------
        cursor : typing.Optional[str]
            Used for fetching next page. Cursor is returned in the response.

        page_size : typing.Optional[int]
            How many Agents to return at maximum. Can not exceed 100, defaults to 30.

        search : typing.Optional[str]
            Search by agents name.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetAgentsPageResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.get_agents()


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/convai/agents",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            params={
                "cursor": cursor,
                "page_size": page_size,
                "search": search,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetAgentsPageResponseModel,
                    construct_type(
                        type_=GetAgentsPageResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_conversations(
        self,
        *,
        cursor: typing.Optional[str] = None,
        agent_id: typing.Optional[str] = None,
        call_successful: typing.Optional[EvaluationSuccessResult] = None,
        page_size: typing.Optional[int] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetConversationsPageResponseModel:
        """
        Get all conversations of agents that user owns. With option to restrict to a specific agent.

        Parameters
        ----------
        cursor : typing.Optional[str]
            Used for fetching next page. Cursor is returned in the response.

        agent_id : typing.Optional[str]
            The id of the agent you're taking the action on.

        call_successful : typing.Optional[EvaluationSuccessResult]
            The result of the success evaluation

        page_size : typing.Optional[int]
            How many conversations to return at maximum. Can not exceed 100, defaults to 30.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetConversationsPageResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.get_conversations()


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/convai/conversations",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            params={
                "cursor": cursor,
                "agent_id": agent_id,
                "call_successful": call_successful,
                "page_size": page_size,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetConversationsPageResponseModel,
                    construct_type(
                        type_=GetConversationsPageResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_conversation(
        self,
        conversation_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetConversationResponseModel:
        """
        Get the details of a particular conversation

        Parameters
        ----------
        conversation_id : str
            The id of the conversation you're taking the action on.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetConversationResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.get_conversation(
                conversation_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/conversations/{jsonable_encoder(conversation_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetConversationResponseModel,
                    construct_type(
                        type_=GetConversationResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def delete_conversation(
        self,
        conversation_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Delete a particular conversation

        Parameters
        ----------
        conversation_id : str
            The id of the conversation you're taking the action on.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.delete_conversation(
                conversation_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/conversations/{jsonable_encoder(conversation_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    typing.Optional[typing.Any],
                    construct_type(
                        type_=typing.Optional[typing.Any],  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_conversation_audio(
        self,
        conversation_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.AsyncIterator[bytes]:
        """
        Get the audio recording of a particular conversation

        Parameters
        ----------
        conversation_id : str
            The id of the conversation you're taking the action on.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Yields
        ------
        typing.AsyncIterator[bytes]
            Successful Response
        """
        async with self._client_wrapper.httpx_client.stream(
            f"v1/convai/conversations/{jsonable_encoder(conversation_id)}/audio",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        ) as _response:
            try:
                if 200 <= _response.status_code < 300:
                    _chunk_size = request_options.get("chunk_size", 1024) if request_options is not None else 1024
                    async for _chunk in _response.aiter_bytes(chunk_size=_chunk_size):
                        yield _chunk
                    return
                await _response.aread()
                if _response.status_code == 422:
                    raise UnprocessableEntityError(
                        typing.cast(
                            HttpValidationError,
                            construct_type(
                                type_=HttpValidationError,  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                _response_json = _response.json()
            except JSONDecodeError:
                raise ApiError(status_code=_response.status_code, body=_response.text)
            raise ApiError(status_code=_response.status_code, body=_response_json)

    async def post_conversation_feedback(
        self,
        conversation_id: str,
        *,
        feedback: UserFeedbackScore,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Send the feedback for the given conversation

        Parameters
        ----------
        conversation_id : str
            The id of the conversation you're taking the action on.

        feedback : UserFeedbackScore
            Either 'like' or 'dislike' to indicate the feedback for the conversation.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.post_conversation_feedback(
                conversation_id="21m00Tcm4TlvDq8ikWAM",
                feedback="like",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/conversations/{jsonable_encoder(conversation_id)}/feedback",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "feedback": feedback,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    typing.Optional[typing.Any],
                    construct_type(
                        type_=typing.Optional[typing.Any],  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def create_phone_number(
        self,
        *,
        request: ConversationalAiCreatePhoneNumberRequestBody,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> CreatePhoneNumberResponseModel:
        """
        Import Phone Number from provider configuration (Twilio or SIP trunk)

        Parameters
        ----------
        request : ConversationalAiCreatePhoneNumberRequestBody

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        CreatePhoneNumberResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs, CreateTwilioPhoneNumberRequest

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.create_phone_number(
                request=CreateTwilioPhoneNumberRequest(
                    phone_number="phone_number",
                    label="label",
                    sid="sid",
                    token="token",
                ),
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/convai/phone-numbers/create",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json=convert_and_respect_annotation_metadata(
                object_=request,
                annotation=ConversationalAiCreatePhoneNumberRequestBody,
                direction="write",
            ),
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    CreatePhoneNumberResponseModel,
                    construct_type(
                        type_=CreatePhoneNumberResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_phone_number(
        self,
        phone_number_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetPhoneNumberResponseModel:
        """
        Retrieve Phone Number details by ID

        Parameters
        ----------
        phone_number_id : str
            The id of an agent. This is returned on agent creation.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetPhoneNumberResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.get_phone_number(
                phone_number_id="TeaqRRdTcIfIu2i7BYfT",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/phone-numbers/{jsonable_encoder(phone_number_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetPhoneNumberResponseModel,
                    construct_type(
                        type_=GetPhoneNumberResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def delete_phone_number(
        self,
        phone_number_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Delete Phone Number by ID

        Parameters
        ----------
        phone_number_id : str
            The id of an agent. This is returned on agent creation.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.delete_phone_number(
                phone_number_id="TeaqRRdTcIfIu2i7BYfT",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/phone-numbers/{jsonable_encoder(phone_number_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    typing.Optional[typing.Any],
                    construct_type(
                        type_=typing.Optional[typing.Any],  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def update_phone_number(
        self,
        phone_number_id: str,
        *,
        agent_id: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetPhoneNumberResponseModel:
        """
        Update Phone Number details by ID

        Parameters
        ----------
        phone_number_id : str
            The id of an agent. This is returned on agent creation.

        agent_id : typing.Optional[str]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetPhoneNumberResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.update_phone_number(
                phone_number_id="TeaqRRdTcIfIu2i7BYfT",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/phone-numbers/{jsonable_encoder(phone_number_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="PATCH",
            json={
                "agent_id": agent_id,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetPhoneNumberResponseModel,
                    construct_type(
                        type_=GetPhoneNumberResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_phone_numbers(
        self, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.List[GetPhoneNumberResponseModel]:
        """
        Retrieve all Phone Numbers

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[GetPhoneNumberResponseModel]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.get_phone_numbers()


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/convai/phone-numbers/",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    typing.List[GetPhoneNumberResponseModel],
                    construct_type(
                        type_=typing.List[GetPhoneNumberResponseModel],  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_knowledge_base_list(
        self,
        *,
        cursor: typing.Optional[str] = None,
        page_size: typing.Optional[int] = None,
        search: typing.Optional[str] = None,
        show_only_owned_documents: typing.Optional[bool] = None,
        use_typesense: typing.Optional[bool] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetKnowledgeBaseListResponseModel:
        """
        Get a list of available knowledge base documents

        Parameters
        ----------
        cursor : typing.Optional[str]
            Used for fetching next page. Cursor is returned in the response.

        page_size : typing.Optional[int]
            How many documents to return at maximum. Can not exceed 100, defaults to 30.

        search : typing.Optional[str]
            If specified, the endpoint returns only such knowledge base documents whose names start with this string.

        show_only_owned_documents : typing.Optional[bool]
            If set to true, the endpoint will return only documents owned by you (and not shared from somebody else).

        use_typesense : typing.Optional[bool]
            If set to true, the endpoint will use typesense DB to search for the documents).

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetKnowledgeBaseListResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.get_knowledge_base_list()


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/convai/knowledge-base",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            params={
                "cursor": cursor,
                "page_size": page_size,
                "search": search,
                "show_only_owned_documents": show_only_owned_documents,
                "use_typesense": use_typesense,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetKnowledgeBaseListResponseModel,
                    construct_type(
                        type_=GetKnowledgeBaseListResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def add_to_knowledge_base(
        self,
        *,
        name: typing.Optional[str] = OMIT,
        url: typing.Optional[str] = OMIT,
        file: typing.Optional[core.File] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddKnowledgeBaseResponseModel:
        """
        Uploads a file or reference a webpage to use as part of the shared knowledge base

        Parameters
        ----------
        name : typing.Optional[str]
            A custom, human-readable name for the document.

        url : typing.Optional[str]
            URL to a page of documentation that the agent will have access to in order to interact with users.

        file : typing.Optional[core.File]
            See core.File for more documentation

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddKnowledgeBaseResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.add_to_knowledge_base()


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/convai/knowledge-base",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            data={
                "name": name,
                "url": url,
            },
            files={
                "file": file,
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    AddKnowledgeBaseResponseModel,
                    construct_type(
                        type_=AddKnowledgeBaseResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def create_knowledge_base_url_document(
        self,
        *,
        url: str,
        name: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddKnowledgeBaseResponseModel:
        """
        Create a knowledge base document generated by scraping the given webpage.

        Parameters
        ----------
        url : str
            URL to a page of documentation that the agent will have access to in order to interact with users.

        name : typing.Optional[str]
            A custom, human-readable name for the document.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddKnowledgeBaseResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.create_knowledge_base_url_document(
                url="url",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/convai/knowledge-base/url",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "url": url,
                "name": name,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    AddKnowledgeBaseResponseModel,
                    construct_type(
                        type_=AddKnowledgeBaseResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def create_knowledge_base_file_document(
        self,
        *,
        file: core.File,
        name: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddKnowledgeBaseResponseModel:
        """
        Create a knowledge base document generated form the uploaded file.

        Parameters
        ----------
        file : core.File
            See core.File for more documentation

        name : typing.Optional[str]
            A custom, human-readable name for the document.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddKnowledgeBaseResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.create_knowledge_base_file_document()


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/convai/knowledge-base/file",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            data={
                "name": name,
            },
            files={
                "file": file,
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    AddKnowledgeBaseResponseModel,
                    construct_type(
                        type_=AddKnowledgeBaseResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def create_knowledge_base_text_document(
        self,
        *,
        text: str,
        name: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddKnowledgeBaseResponseModel:
        """
        Create a knowledge base document containing the provided text.

        Parameters
        ----------
        text : str
            Text content to be added to the knowledge base.

        name : typing.Optional[str]
            A custom, human-readable name for the document.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddKnowledgeBaseResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.create_knowledge_base_text_document(
                text="text",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/convai/knowledge-base/text",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "text": text,
                "name": name,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    AddKnowledgeBaseResponseModel,
                    construct_type(
                        type_=AddKnowledgeBaseResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def rag_index_status(
        self,
        documentation_id: str,
        *,
        model: EmbeddingModelEnum,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> RagIndexResponseModel:
        """
        In case the document is not RAG indexed, it triggers rag indexing task, otherwise it just returns the current status.

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        model : EmbeddingModelEnum

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        RagIndexResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.rag_index_status(
                documentation_id="21m00Tcm4TlvDq8ikWAM",
                model="e5_mistral_7b_instruct",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/knowledge-base/{jsonable_encoder(documentation_id)}/rag-index",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "model": model,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    RagIndexResponseModel,
                    construct_type(
                        type_=RagIndexResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_knowledge_base_document_by_id(
        self,
        documentation_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ConversationalAiGetKnowledgeBaseDocumentByIdResponse:
        """
        Get details about a specific documentation making up the agent's knowledge base

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ConversationalAiGetKnowledgeBaseDocumentByIdResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.get_knowledge_base_document_by_id(
                documentation_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/knowledge-base/{jsonable_encoder(documentation_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    ConversationalAiGetKnowledgeBaseDocumentByIdResponse,
                    construct_type(
                        type_=ConversationalAiGetKnowledgeBaseDocumentByIdResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def delete_knowledge_base_document(
        self,
        documentation_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Delete a document from the knowledge base

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.delete_knowledge_base_document(
                documentation_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/knowledge-base/{jsonable_encoder(documentation_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    typing.Optional[typing.Any],
                    construct_type(
                        type_=typing.Optional[typing.Any],  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_dependent_agents(
        self,
        documentation_id: str,
        *,
        cursor: typing.Optional[str] = None,
        page_size: typing.Optional[int] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetKnowledgeBaseDependentAgentsResponseModel:
        """
        Get a list of agents depending on this knowledge base document

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        cursor : typing.Optional[str]
            Used for fetching next page. Cursor is returned in the response.

        page_size : typing.Optional[int]
            How many documents to return at maximum. Can not exceed 100, defaults to 30.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetKnowledgeBaseDependentAgentsResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.get_dependent_agents(
                documentation_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/knowledge-base/{jsonable_encoder(documentation_id)}/dependent-agents",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            params={
                "cursor": cursor,
                "page_size": page_size,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetKnowledgeBaseDependentAgentsResponseModel,
                    construct_type(
                        type_=GetKnowledgeBaseDependentAgentsResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_knowledge_base_document_content(
        self,
        documentation_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> None:
        """
        Get the entire content of a document from the knowledge base

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.get_knowledge_base_document_content(
                documentation_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/knowledge-base/{jsonable_encoder(documentation_id)}/content",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_knowledge_base_document_part_by_id(
        self,
        documentation_id: str,
        chunk_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> KnowledgeBaseDocumentChunkResponseModel:
        """
        Get details about a specific documentation part used by RAG.

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        chunk_id : str
            The id of a document RAG chunk from the knowledge base.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        KnowledgeBaseDocumentChunkResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.get_knowledge_base_document_part_by_id(
                documentation_id="21m00Tcm4TlvDq8ikWAM",
                chunk_id="chunk_id",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/knowledge-base/{jsonable_encoder(documentation_id)}/chunk/{jsonable_encoder(chunk_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    KnowledgeBaseDocumentChunkResponseModel,
                    construct_type(
                        type_=KnowledgeBaseDocumentChunkResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_settings(
        self, *, request_options: typing.Optional[RequestOptions] = None
    ) -> GetConvAiSettingsResponseModel:
        """
        Retrieve Convai settings for the workspace

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetConvAiSettingsResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.get_settings()


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/convai/settings",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetConvAiSettingsResponseModel,
                    construct_type(
                        type_=GetConvAiSettingsResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def update_settings(
        self,
        *,
        conversation_initiation_client_data_webhook: typing.Optional[ConversationInitiationClientDataWebhook] = OMIT,
        webhooks: typing.Optional[ConvAiWebhooks] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetConvAiSettingsResponseModel:
        """
        Update Convai settings for the workspace

        Parameters
        ----------
        conversation_initiation_client_data_webhook : typing.Optional[ConversationInitiationClientDataWebhook]

        webhooks : typing.Optional[ConvAiWebhooks]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetConvAiSettingsResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.update_settings()


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/convai/settings",
            base_url=self._client_wrapper.get_environment().base,
            method="PATCH",
            json={
                "conversation_initiation_client_data_webhook": convert_and_respect_annotation_metadata(
                    object_=conversation_initiation_client_data_webhook,
                    annotation=ConversationInitiationClientDataWebhook,
                    direction="write",
                ),
                "webhooks": convert_and_respect_annotation_metadata(
                    object_=webhooks, annotation=ConvAiWebhooks, direction="write"
                ),
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetConvAiSettingsResponseModel,
                    construct_type(
                        type_=GetConvAiSettingsResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_secrets(
        self, *, request_options: typing.Optional[RequestOptions] = None
    ) -> GetWorkspaceSecretsResponseModel:
        """
        Get all workspace secrets for the user

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetWorkspaceSecretsResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.get_secrets()


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/convai/secrets",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetWorkspaceSecretsResponseModel,
                    construct_type(
                        type_=GetWorkspaceSecretsResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def create_secret(
        self,
        *,
        name: str,
        value: str,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> PostWorkspaceSecretResponseModel:
        """
        Create a new secret for the workspace

        Parameters
        ----------
        name : str

        value : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        PostWorkspaceSecretResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.create_secret(
                name="name",
                value="value",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/convai/secrets",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "name": name,
                "value": value,
                "type": "new",
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    PostWorkspaceSecretResponseModel,
                    construct_type(
                        type_=PostWorkspaceSecretResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def delete_secret(self, secret_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> None:
        """
        Delete a workspace secret if it's not in use

        Parameters
        ----------
        secret_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.delete_secret(
                secret_id="secret_id",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/secrets/{jsonable_encoder(secret_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)
