"""
نموذج المستخدم
User Model
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from passlib.context import CryptContext
import enum
from datetime import datetime
from typing import Optional

from app.core.database import Base

# إعداد تشفير كلمات المرور
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class UserRole(str, enum.Enum):
    """أدوار المستخدمين"""
    ADMIN = "admin"
    USER = "user"
    PREMIUM = "premium"
    MODERATOR = "moderator"


class UserStatus(str, enum.Enum):
    """حالات المستخدم"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING = "pending"


class User(Base):
    """نموذج المستخدم"""
    __tablename__ = "users"
    
    # المعرف الفريد
    id = Column(Integer, primary_key=True, index=True)
    
    # معلومات أساسية
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(255), unique=True, index=True, nullable=False)
    full_name = Column(String(100), nullable=False)
    
    # كلمة المرور
    hashed_password = Column(String(255), nullable=False)
    
    # الدور والحالة
    role = Column(Enum(UserRole), default=UserRole.USER, nullable=False)
    status = Column(Enum(UserStatus), default=UserStatus.PENDING, nullable=False)
    
    # معلومات إضافية
    phone = Column(String(20), nullable=True)
    avatar_url = Column(String(500), nullable=True)
    bio = Column(Text, nullable=True)
    
    # إعدادات المستخدم
    language = Column(String(10), default="ar", nullable=False)
    timezone = Column(String(50), default="Asia/Riyadh", nullable=False)
    
    # حالة التفعيل
    is_active = Column(Boolean, default=False, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    is_premium = Column(Boolean, default=False, nullable=False)
    
    # تواريخ مهمة
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), nullable=True)
    last_login = Column(DateTime(timezone=True), nullable=True)
    email_verified_at = Column(DateTime(timezone=True), nullable=True)
    
    # إحصائيات
    login_count = Column(Integer, default=0, nullable=False)
    projects_count = Column(Integer, default=0, nullable=False)
    videos_created = Column(Integer, default=0, nullable=False)
    
    # العلاقات
    projects = relationship("Project", back_populates="owner", cascade="all, delete-orphan")
    templates = relationship("Template", back_populates="creator", cascade="all, delete-orphan")
    media_files = relationship("MediaFile", back_populates="owner", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"
    
    def verify_password(self, password: str) -> bool:
        """التحقق من كلمة المرور"""
        return pwd_context.verify(password, self.hashed_password)
    
    @staticmethod
    def hash_password(password: str) -> str:
        """تشفير كلمة المرور"""
        return pwd_context.hash(password)
    
    def set_password(self, password: str):
        """تعيين كلمة مرور جديدة"""
        self.hashed_password = self.hash_password(password)
    
    @property
    def is_admin(self) -> bool:
        """التحقق من كون المستخدم مدير"""
        return self.role == UserRole.ADMIN
    
    @property
    def is_moderator(self) -> bool:
        """التحقق من كون المستخدم مشرف"""
        return self.role in [UserRole.ADMIN, UserRole.MODERATOR]
    
    @property
    def can_create_premium_content(self) -> bool:
        """التحقق من إمكانية إنشاء محتوى مميز"""
        return self.is_premium or self.is_admin
    
    def update_last_login(self):
        """تحديث آخر تسجيل دخول"""
        self.last_login = datetime.utcnow()
        self.login_count += 1
    
    def activate(self):
        """تفعيل المستخدم"""
        self.is_active = True
        self.status = UserStatus.ACTIVE
    
    def deactivate(self):
        """إلغاء تفعيل المستخدم"""
        self.is_active = False
        self.status = UserStatus.INACTIVE
    
    def suspend(self):
        """تعليق المستخدم"""
        self.is_active = False
        self.status = UserStatus.SUSPENDED
    
    def verify_email(self):
        """تأكيد البريد الإلكتروني"""
        self.is_verified = True
        self.email_verified_at = datetime.utcnow()
        if self.status == UserStatus.PENDING:
            self.activate()
    
    def upgrade_to_premium(self):
        """ترقية إلى حساب مميز"""
        self.is_premium = True
        self.role = UserRole.PREMIUM
    
    def downgrade_from_premium(self):
        """إلغاء الحساب المميز"""
        self.is_premium = False
        if self.role == UserRole.PREMIUM:
            self.role = UserRole.USER
    
    def to_dict(self, include_sensitive: bool = False) -> dict:
        """تحويل إلى قاموس"""
        data = {
            "id": self.id,
            "username": self.username,
            "email": self.email,
            "full_name": self.full_name,
            "role": self.role.value,
            "status": self.status.value,
            "phone": self.phone,
            "avatar_url": self.avatar_url,
            "bio": self.bio,
            "language": self.language,
            "timezone": self.timezone,
            "is_active": self.is_active,
            "is_verified": self.is_verified,
            "is_premium": self.is_premium,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_login": self.last_login.isoformat() if self.last_login else None,
            "email_verified_at": self.email_verified_at.isoformat() if self.email_verified_at else None,
            "login_count": self.login_count,
            "projects_count": self.projects_count,
            "videos_created": self.videos_created,
        }
        
        if include_sensitive:
            data["hashed_password"] = self.hashed_password
        
        return data


class UserSession(Base):
    """جلسات المستخدم"""
    __tablename__ = "user_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, nullable=False, index=True)
    session_token = Column(String(255), unique=True, nullable=False)
    refresh_token = Column(String(255), unique=True, nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    last_used = Column(DateTime(timezone=True), nullable=True)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    
    def __repr__(self):
        return f"<UserSession(id={self.id}, user_id={self.user_id})>"


class UserPreferences(Base):
    """تفضيلات المستخدم"""
    __tablename__ = "user_preferences"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, nullable=False, unique=True, index=True)
    
    # تفضيلات الفيديو
    default_video_quality = Column(String(20), default="1080p", nullable=False)
    default_video_format = Column(String(10), default="mp4", nullable=False)
    default_aspect_ratio = Column(String(10), default="16:9", nullable=False)
    
    # تفضيلات الصوت
    default_voice_id = Column(String(50), nullable=True)
    default_voice_speed = Column(String(20), default="normal", nullable=False)
    default_voice_style = Column(String(20), default="natural", nullable=False)
    
    # تفضيلات التصميم
    default_template_id = Column(Integer, nullable=True)
    default_color_scheme = Column(String(20), default="blue", nullable=False)
    default_font_family = Column(String(50), default="Arial", nullable=False)
    
    # إعدادات الإشعارات
    email_notifications = Column(Boolean, default=True, nullable=False)
    push_notifications = Column(Boolean, default=True, nullable=False)
    marketing_emails = Column(Boolean, default=False, nullable=False)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), nullable=True)
    
    def __repr__(self):
        return f"<UserPreferences(id={self.id}, user_id={self.user_id})>"
