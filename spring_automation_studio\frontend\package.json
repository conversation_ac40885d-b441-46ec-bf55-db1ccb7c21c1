{"name": "spring-automation-studio-frontend", "version": "2.0.0", "description": "ستوديو الربيع للأتمتة - واجهة المستخدم", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "analyze": "npm run build && npx vite-bundle-analyzer dist/stats.html"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "zustand": "^4.4.7", "axios": "^1.6.2", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.5", "lucide-react": "^0.294.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-popover": "^1.0.7", "react-dropzone": "^14.2.3", "react-player": "^2.13.0", "wavesurfer.js": "^7.4.4", "fabric": "^5.3.0", "konva": "^9.2.0", "react-konva": "^18.2.10", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-beautiful-dnd": "^13.1.1", "react-color": "^2.19.3", "react-webcam": "^7.2.0", "socket.io-client": "^4.7.4", "i18next": "^23.7.6", "react-i18next": "^13.5.0", "i18next-browser-languagedetector": "^7.2.0", "react-helmet-async": "^2.0.4", "react-intersection-observer": "^9.5.3", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "react-window-infinite-loader": "^1.0.9"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-color": "^3.0.9", "@types/react-window": "^1.8.8", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "vite": "^5.0.0", "typescript": "^5.2.2", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "prettier": "^3.1.0", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "vitest": "^1.0.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "jsdom": "^23.0.1", "vite-bundle-analyzer": "^0.7.0", "vite-plugin-pwa": "^0.17.4", "workbox-window": "^7.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["react", "typescript", "vite", "tailwindcss", "ai", "video-generation", "arabic", "automation", "content-creation"], "author": {"name": "Spring Studio Team", "email": "<EMAIL>", "url": "https://spring-studio.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/spring-studio/automation-studio.git"}, "bugs": {"url": "https://github.com/spring-studio/automation-studio/issues"}, "homepage": "https://spring-studio.com"}