# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
import pydantic
from .client_event import ClientEvent
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class ConversationConfig(UncheckedBaseModel):
    max_duration_seconds: typing.Optional[int] = pydantic.Field(default=None)
    """
    The maximum duration of a conversation in seconds
    """

    client_events: typing.Optional[typing.List[ClientEvent]] = pydantic.Field(default=None)
    """
    The events that will be sent to the client
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
