# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from .safety_evaluation import SafetyEvaluation
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic


class SafetyCommonModel(UncheckedBaseModel):
    """
    Safety object that has the information of safety evaluations based on used voice.
    """

    ivc: typing.Optional[SafetyEvaluation] = None
    non_ivc: typing.Optional[SafetyEvaluation] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
