"""
خدمة إنتاج الفيديو
Video Production Service
"""

import os
import uuid
import requests
from pathlib import Path
from typing import Optional, Dict, Any, List
import logging
from PIL import Image, ImageDraw, ImageFont
import json

logger = logging.getLogger(__name__)


class VideoService:
    """خدمة إنتاج الفيديو"""
    
    def __init__(self):
        self.output_dir = Path("outputs")
        self.temp_dir = Path("temp")
        self.uploads_dir = Path("uploads")
        
        # إنشاء المجلدات إذا لم تكن موجودة
        self.output_dir.mkdir(exist_ok=True)
        self.temp_dir.mkdir(exist_ok=True)
        self.uploads_dir.mkdir(exist_ok=True)
    
    def generate_image_with_pollinations(self, prompt: str, width: int = 1080, height: int = 1920) -> Optional[str]:
        """توليد صورة باستخدام Pollinations AI"""
        try:
            # ترجمة النص إلى الإنجليزية إذا كان عربياً
            if self._is_arabic(prompt):
                prompt = self._translate_to_english(prompt)
            
            # تنظيف النص
            prompt = prompt.replace(" ", "%20").replace(",", "%2C")
            
            # رابط API
            url = f"https://image.pollinations.ai/prompt/{prompt}?width={width}&height={height}&seed={uuid.uuid4().hex[:8]}"
            
            # تحميل الصورة
            response = requests.get(url, timeout=30)
            
            if response.status_code == 200:
                # حفظ الصورة
                image_filename = f"generated_image_{uuid.uuid4().hex[:8]}.jpg"
                image_path = self.temp_dir / image_filename
                
                with open(image_path, 'wb') as f:
                    f.write(response.content)
                
                logger.info(f"Image generated successfully: {image_path}")
                return str(image_path)
            else:
                logger.error(f"Failed to generate image: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error generating image: {e}")
            return None
    
    def create_text_overlay_image(self, text: str, width: int = 1080, height: int = 1920, 
                                 background_color: str = "#1e3a8a", text_color: str = "white") -> str:
        """إنشاء صورة مع نص"""
        try:
            # إنشاء صورة جديدة
            image = Image.new('RGB', (width, height), background_color)
            draw = ImageDraw.Draw(image)
            
            # محاولة استخدام خط عربي
            try:
                font_size = 60
                font = ImageFont.truetype("arial.ttf", font_size)
            except:
                font = ImageFont.load_default()
            
            # حساب موقع النص
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            # تقسيم النص إذا كان طويلاً
            if text_width > width - 100:
                words = text.split()
                lines = []
                current_line = ""
                
                for word in words:
                    test_line = current_line + " " + word if current_line else word
                    bbox = draw.textbbox((0, 0), test_line, font=font)
                    if bbox[2] - bbox[0] <= width - 100:
                        current_line = test_line
                    else:
                        if current_line:
                            lines.append(current_line)
                        current_line = word
                
                if current_line:
                    lines.append(current_line)
                
                # رسم النص متعدد الأسطر
                total_height = len(lines) * (text_height + 10)
                start_y = (height - total_height) // 2
                
                for i, line in enumerate(lines):
                    bbox = draw.textbbox((0, 0), line, font=font)
                    line_width = bbox[2] - bbox[0]
                    x = (width - line_width) // 2
                    y = start_y + i * (text_height + 10)
                    draw.text((x, y), line, fill=text_color, font=font)
            else:
                # رسم النص في سطر واحد
                x = (width - text_width) // 2
                y = (height - text_height) // 2
                draw.text((x, y), text, fill=text_color, font=font)
            
            # حفظ الصورة
            image_filename = f"text_overlay_{uuid.uuid4().hex[:8]}.jpg"
            image_path = self.temp_dir / image_filename
            image.save(image_path, "JPEG", quality=95)
            
            logger.info(f"Text overlay image created: {image_path}")
            return str(image_path)
            
        except Exception as e:
            logger.error(f"Error creating text overlay: {e}")
            # إنشاء صورة بسيطة كبديل
            return self._create_simple_image(text, width, height)
    
    def save_audio_file(self, audio_data: bytes, filename: Optional[str] = None) -> str:
        """حفظ ملف صوتي"""
        if not filename:
            filename = f"audio_{uuid.uuid4().hex[:8]}.mp3"
        
        audio_path = self.temp_dir / filename
        
        with open(audio_path, 'wb') as f:
            f.write(audio_data)
        
        logger.info(f"Audio file saved: {audio_path}")
        return str(audio_path)
    
    def create_simple_video_info(self, script_data: Dict[str, Any], 
                                image_paths: List[str], audio_path: str) -> Dict[str, Any]:
        """إنشاء معلومات فيديو بسيط"""
        try:
            video_info = {
                "id": uuid.uuid4().hex,
                "script": script_data,
                "images": image_paths,
                "audio": audio_path,
                "duration": script_data.get("duration_estimate", 30),
                "status": "ready",
                "created_at": str(Path().cwd()),
                "output_format": "mp4",
                "resolution": "1080x1920"
            }
            
            # حفظ معلومات الفيديو
            info_filename = f"video_info_{video_info['id']}.json"
            info_path = self.output_dir / info_filename
            
            with open(info_path, 'w', encoding='utf-8') as f:
                json.dump(video_info, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Video info created: {info_path}")
            return video_info
            
        except Exception as e:
            logger.error(f"Error creating video info: {e}")
            return {}
    
    def _is_arabic(self, text: str) -> bool:
        """التحقق من وجود نص عربي"""
        arabic_chars = set('ابتثجحخدذرزسشصضطظعغفقكلمنهوي')
        return any(char in arabic_chars for char in text)
    
    def _translate_to_english(self, text: str) -> str:
        """ترجمة بسيطة للنص العربي"""
        # ترجمة بسيطة لبعض الكلمات الشائعة
        translations = {
            "تعليم": "education",
            "تعلم": "learning", 
            "علم": "science",
            "تقنية": "technology",
            "برمجة": "programming",
            "ذكاء اصطناعي": "artificial intelligence",
            "كمبيوتر": "computer",
            "إنترنت": "internet",
            "معلومات": "information",
            "بيانات": "data"
        }
        
        for arabic, english in translations.items():
            text = text.replace(arabic, english)
        
        return text
    
    def _create_simple_image(self, text: str, width: int, height: int) -> str:
        """إنشاء صورة بسيطة كبديل"""
        try:
            image = Image.new('RGB', (width, height), '#2563eb')
            draw = ImageDraw.Draw(image)
            
            # استخدام خط افتراضي
            font = ImageFont.load_default()
            
            # رسم النص في المنتصف
            bbox = draw.textbbox((0, 0), text[:50], font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            x = (width - text_width) // 2
            y = (height - text_height) // 2
            
            draw.text((x, y), text[:50], fill='white', font=font)
            
            # حفظ الصورة
            image_filename = f"simple_image_{uuid.uuid4().hex[:8]}.jpg"
            image_path = self.temp_dir / image_filename
            image.save(image_path, "JPEG")
            
            return str(image_path)
            
        except Exception as e:
            logger.error(f"Error creating simple image: {e}")
            return ""


# إنشاء مثيل من الخدمة
video_service = VideoService()
