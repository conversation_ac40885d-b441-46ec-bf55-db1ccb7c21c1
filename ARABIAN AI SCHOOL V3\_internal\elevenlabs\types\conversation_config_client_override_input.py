# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from .agent_config_override import AgentConfigOverride
import pydantic
from .tts_conversational_config_override import TtsConversationalConfigOverride
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class ConversationConfigClientOverrideInput(UncheckedBaseModel):
    agent: typing.Optional[AgentConfigOverride] = pydantic.Field(default=None)
    """
    The overrides for the agent configuration
    """

    tts: typing.Optional[TtsConversationalConfigOverride] = pydantic.Field(default=None)
    """
    The overrides for the TTS configuration
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
