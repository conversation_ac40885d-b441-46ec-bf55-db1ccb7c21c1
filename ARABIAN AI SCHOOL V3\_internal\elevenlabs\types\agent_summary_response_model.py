# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import pydantic
from .resource_access_info import ResourceAccessInfo
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import typing


class AgentSummaryResponseModel(UncheckedBaseModel):
    agent_id: str = pydantic.Field()
    """
    The ID of the agent
    """

    name: str = pydantic.Field()
    """
    The name of the agent
    """

    created_at_unix_secs: int = pydantic.Field()
    """
    The creation time of the agent in unix seconds
    """

    access_info: ResourceAccessInfo = pydantic.Field()
    """
    The access information of the agent
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
