"""
إعدادات النظام الأساسية
System Configuration Settings
"""

from pydantic_settings import BaseSettings
from pydantic import Field, validator
from typing import List, Optional
import os
from pathlib import Path


class Settings(BaseSettings):
    """إعدادات التطبيق الرئيسية"""
    
    # إعدادات أساسية
    APP_NAME: str = "ستوديو الربيع للأتمتة"
    APP_VERSION: str = "2.0.0"
    DEBUG: bool = Field(default=False, env="DEBUG")
    SECRET_KEY: str = Field(..., env="SECRET_KEY")
    
    # إعدادات الخادم
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8000, env="PORT")
    ALLOWED_HOSTS: List[str] = Field(
        default=["localhost", "127.0.0.1", "0.0.0.0"],
        env="ALLOWED_HOSTS"
    )
    
    # إعدادات قاعدة البيانات
    DATABASE_URL: str = Field(..., env="DATABASE_URL")
    DATABASE_ECHO: bool = Field(default=False, env="DATABASE_ECHO")
    
    # إعدادات Redis
    REDIS_URL: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    REDIS_DB: int = Field(default=0, env="REDIS_DB")
    
    # إعدادات JWT
    JWT_SECRET_KEY: str = Field(..., env="JWT_SECRET_KEY")
    JWT_ALGORITHM: str = Field(default="HS256", env="JWT_ALGORITHM")
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, env="JWT_ACCESS_TOKEN_EXPIRE_MINUTES")
    JWT_REFRESH_TOKEN_EXPIRE_DAYS: int = Field(default=7, env="JWT_REFRESH_TOKEN_EXPIRE_DAYS")
    
    # مفاتيح خدمات الذكاء الاصطناعي
    OPENAI_API_KEY: str = Field(..., env="OPENAI_API_KEY")
    GEMINI_API_KEY: str = Field(..., env="GEMINI_API_KEY")
    ELEVENLABS_API_KEY: str = Field(..., env="ELEVENLABS_API_KEY")
    REPLICATE_API_TOKEN: str = Field(..., env="REPLICATE_API_TOKEN")
    
    # إعدادات OpenAI
    OPENAI_MODEL: str = Field(default="gpt-4-turbo-preview", env="OPENAI_MODEL")
    OPENAI_MAX_TOKENS: int = Field(default=4000, env="OPENAI_MAX_TOKENS")
    OPENAI_TEMPERATURE: float = Field(default=0.7, env="OPENAI_TEMPERATURE")
    
    # إعدادات ElevenLabs
    ELEVENLABS_MODEL: str = Field(default="eleven_multilingual_v2", env="ELEVENLABS_MODEL")
    ELEVENLABS_VOICE_STABILITY: float = Field(default=0.5, env="ELEVENLABS_VOICE_STABILITY")
    ELEVENLABS_VOICE_SIMILARITY: float = Field(default=0.8, env="ELEVENLABS_VOICE_SIMILARITY")
    
    # إعدادات الملفات
    UPLOAD_DIR: str = Field(default="uploads", env="UPLOAD_DIR")
    OUTPUT_DIR: str = Field(default="outputs", env="OUTPUT_DIR")
    TEMP_DIR: str = Field(default="temp", env="TEMP_DIR")
    MAX_FILE_SIZE: int = Field(default=100 * 1024 * 1024, env="MAX_FILE_SIZE")  # 100MB
    ALLOWED_FILE_TYPES: List[str] = Field(
        default=["jpg", "jpeg", "png", "gif", "mp4", "mp3", "wav", "txt", "pdf"],
        env="ALLOWED_FILE_TYPES"
    )
    
    # إعدادات الفيديو
    VIDEO_MAX_DURATION: int = Field(default=300, env="VIDEO_MAX_DURATION")  # 5 minutes
    VIDEO_DEFAULT_FPS: int = Field(default=30, env="VIDEO_DEFAULT_FPS")
    VIDEO_DEFAULT_RESOLUTION: str = Field(default="1080x1920", env="VIDEO_DEFAULT_RESOLUTION")
    VIDEO_FORMATS: List[str] = Field(default=["mp4", "avi", "mov"], env="VIDEO_FORMATS")
    
    # إعدادات الصوت
    AUDIO_SAMPLE_RATE: int = Field(default=44100, env="AUDIO_SAMPLE_RATE")
    AUDIO_CHANNELS: int = Field(default=2, env="AUDIO_CHANNELS")
    AUDIO_FORMATS: List[str] = Field(default=["mp3", "wav", "aac"], env="AUDIO_FORMATS")
    
    # إعدادات التخزين المؤقت
    CACHE_TTL: int = Field(default=3600, env="CACHE_TTL")  # 1 hour
    CACHE_MAX_SIZE: int = Field(default=1000, env="CACHE_MAX_SIZE")
    
    # إعدادات المهام غير المتزامنة
    CELERY_BROKER_URL: str = Field(default="redis://localhost:6379/1", env="CELERY_BROKER_URL")
    CELERY_RESULT_BACKEND: str = Field(default="redis://localhost:6379/1", env="CELERY_RESULT_BACKEND")
    
    # إعدادات البريد الإلكتروني
    SMTP_HOST: Optional[str] = Field(default=None, env="SMTP_HOST")
    SMTP_PORT: int = Field(default=587, env="SMTP_PORT")
    SMTP_USERNAME: Optional[str] = Field(default=None, env="SMTP_USERNAME")
    SMTP_PASSWORD: Optional[str] = Field(default=None, env="SMTP_PASSWORD")
    SMTP_USE_TLS: bool = Field(default=True, env="SMTP_USE_TLS")
    
    # إعدادات المراقبة
    SENTRY_DSN: Optional[str] = Field(default=None, env="SENTRY_DSN")
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FORMAT: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        env="LOG_FORMAT"
    )
    
    # إعدادات الأمان
    CORS_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://127.0.0.1:3000"],
        env="CORS_ORIGINS"
    )
    RATE_LIMIT_PER_MINUTE: int = Field(default=60, env="RATE_LIMIT_PER_MINUTE")
    
    # إعدادات النسخ الاحتياطي
    BACKUP_ENABLED: bool = Field(default=True, env="BACKUP_ENABLED")
    BACKUP_INTERVAL_HOURS: int = Field(default=24, env="BACKUP_INTERVAL_HOURS")
    BACKUP_RETENTION_DAYS: int = Field(default=30, env="BACKUP_RETENTION_DAYS")
    
    @validator("ALLOWED_HOSTS", pre=True)
    def parse_allowed_hosts(cls, v):
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v
    
    @validator("CORS_ORIGINS", pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("ALLOWED_FILE_TYPES", pre=True)
    def parse_allowed_file_types(cls, v):
        if isinstance(v, str):
            return [ext.strip().lower() for ext in v.split(",")]
        return v
    
    @property
    def database_url_async(self) -> str:
        """رابط قاعدة البيانات للاتصال غير المتزامن"""
        return self.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://")
    
    @property
    def upload_path(self) -> Path:
        """مسار مجلد الرفع"""
        path = Path(self.UPLOAD_DIR)
        path.mkdir(exist_ok=True)
        return path
    
    @property
    def output_path(self) -> Path:
        """مسار مجلد الإخراج"""
        path = Path(self.OUTPUT_DIR)
        path.mkdir(exist_ok=True)
        return path
    
    @property
    def temp_path(self) -> Path:
        """مسار المجلد المؤقت"""
        path = Path(self.TEMP_DIR)
        path.mkdir(exist_ok=True)
        return path
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# إنشاء مثيل الإعدادات
settings = Settings()


# إعدادات خاصة بالبيئة
class DevelopmentSettings(Settings):
    """إعدادات بيئة التطوير"""
    DEBUG: bool = True
    DATABASE_ECHO: bool = True
    LOG_LEVEL: str = "DEBUG"


class ProductionSettings(Settings):
    """إعدادات بيئة الإنتاج"""
    DEBUG: bool = False
    DATABASE_ECHO: bool = False
    LOG_LEVEL: str = "INFO"


class TestingSettings(Settings):
    """إعدادات بيئة الاختبار"""
    DEBUG: bool = True
    DATABASE_URL: str = "sqlite:///./test.db"
    REDIS_URL: str = "redis://localhost:6379/15"


def get_settings() -> Settings:
    """الحصول على الإعدادات حسب البيئة"""
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        return ProductionSettings()
    elif env == "testing":
        return TestingSettings()
    else:
        return DevelopmentSettings()


# تصدير الإعدادات
settings = get_settings()
