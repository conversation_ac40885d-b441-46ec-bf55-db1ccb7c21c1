# This file was auto-generated by <PERSON>rn from our API Definition.

import typing
from ..core.client_wrapper import SyncClient<PERSON>rapper
from .. import core
from ..core.request_options import RequestOptions
from ..types.forced_alignment_response_model import ForcedAlignmentResponseModel
from ..core.unchecked_base_model import construct_type
from ..errors.unprocessable_entity_error import UnprocessableEntityError
from ..types.http_validation_error import HttpValidationError
from json.decoder import J<PERSON><PERSON><PERSON>odeError
from ..core.api_error import ApiError
from ..core.client_wrapper import AsyncClientWrapper

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class ForcedAlignmentClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def create(
        self,
        *,
        file: core.File,
        text: str,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ForcedAlignmentResponseModel:
        """
        Force align an audio file to text. Use this endpoint to get the timing information for each character and word in an audio file based on a provided text transcript.

        Parameters
        ----------
        file : core.File
            See core.File for more documentation

        text : str
            The text to align with the audio or video. The input text can be in any format, however diarization is not supported at this time.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ForcedAlignmentResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.forced_alignment.create(
            text="text",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/forced-alignment",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            data={
                "text": text,
            },
            files={
                "file": file,
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    ForcedAlignmentResponseModel,
                    construct_type(
                        type_=ForcedAlignmentResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)


class AsyncForcedAlignmentClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def create(
        self,
        *,
        file: core.File,
        text: str,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ForcedAlignmentResponseModel:
        """
        Force align an audio file to text. Use this endpoint to get the timing information for each character and word in an audio file based on a provided text transcript.

        Parameters
        ----------
        file : core.File
            See core.File for more documentation

        text : str
            The text to align with the audio or video. The input text can be in any format, however diarization is not supported at this time.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ForcedAlignmentResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.forced_alignment.create(
                text="text",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/forced-alignment",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            data={
                "text": text,
            },
            files={
                "file": file,
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    ForcedAlignmentResponseModel,
                    construct_type(
                        type_=ForcedAlignmentResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)
