# 🌸 ستوديو الربيع للأتمتة
## Spring Automation Studio

### نظام متطور لإنتاج المحتوى التعليمي بالذكاء الاصطناعي

---

## 📖 نظرة عامة

ستوديو الربيع للأتمتة هو نظام متطور ومحسن لإنتاج المحتوى التعليمي والتسويقي باستخدام أحدث تقنيات الذكاء الاصطناعي. يتيح للمستخدمين إنشاء فيديوهات احترافية من مجرد فكرة بسيطة.

## ✨ الميزات الرئيسية

### 🎯 الميزات الأساسية
- **إنتاج فيديو كامل من فكرة واحدة**
- **دعم كامل للغة العربية**
- **أصوات طبيعية متعددة**
- **صور مولدة بالذكاء الاصطناعي**
- **تصدير بجودة عالية**

### 🚀 الميزات المتقدمة
- **واجهة مستخدم عصرية وسهلة**
- **نظام إدارة المشاريع**
- **معاينة فورية للمحتوى**
- **قوالب متعددة للفيديوهات**
- **تخصيص شامل للتصميم**
- **نظام المستخدمين والملفات الشخصية**
- **مكتبة وسائط شخصية**
- **تحليلات وإحصائيات**

## 🛠️ التقنيات المستخدمة

### Backend
- **FastAPI** - إطار عمل سريع وحديث
- **Python 3.11+** - لغة البرمجة الأساسية
- **SQLAlchemy** - قاعدة البيانات
- **Redis** - التخزين المؤقت
- **Celery** - المهام غير المتزامنة

### Frontend
- **React 18** - مكتبة واجهة المستخدم
- **TypeScript** - لغة البرمجة المحسنة
- **Tailwind CSS** - إطار عمل التصميم
- **Zustand** - إدارة الحالة
- **React Query** - إدارة البيانات

### خدمات الذكاء الاصطناعي
- **OpenAI GPT-4** - توليد النصوص
- **Google Gemini** - معالجة المحتوى
- **ElevenLabs** - تحويل النص إلى كلام
- **DALL-E 3** - توليد الصور
- **Runway ML** - توليد الفيديوهات

## 📁 هيكل المشروع

```
spring_automation_studio/
├── backend/                 # النظام الخلفي
│   ├── app/
│   │   ├── api/            # نقاط النهاية
│   │   ├── core/           # الإعدادات الأساسية
│   │   ├── models/         # نماذج قاعدة البيانات
│   │   ├── services/       # خدمات العمل
│   │   └── utils/          # أدوات مساعدة
│   ├── tests/              # الاختبارات
│   └── requirements.txt    # المتطلبات
├── frontend/               # واجهة المستخدم
│   ├── src/
│   │   ├── components/     # المكونات
│   │   ├── pages/          # الصفحات
│   │   ├── hooks/          # React Hooks
│   │   ├── services/       # خدمات API
│   │   └── utils/          # أدوات مساعدة
│   ├── public/             # الملفات العامة
│   └── package.json        # إعدادات المشروع
├── docker/                 # إعدادات Docker
├── docs/                   # الوثائق
└── scripts/                # سكريبتات التشغيل
```

## 🚀 التثبيت والتشغيل

### المتطلبات الأساسية
- Python 3.11+
- Node.js 18+
- Docker (اختياري)
- Redis
- PostgreSQL

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/spring-automation-studio.git
cd spring-automation-studio
```

2. **إعداد النظام الخلفي**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
```

3. **إعداد واجهة المستخدم**
```bash
cd frontend
npm install
```

4. **إعداد متغيرات البيئة**
```bash
cp .env.example .env
# قم بتعديل الملف وإضافة مفاتيح API الخاصة بك
```

5. **تشغيل النظام**
```bash
# النظام الخلفي
cd backend
uvicorn app.main:app --reload

# واجهة المستخدم
cd frontend
npm run dev
```

## 🔧 الإعداد

### مفاتيح API المطلوبة
- `OPENAI_API_KEY` - مفتاح OpenAI
- `GEMINI_API_KEY` - مفتاح Google Gemini
- `ELEVENLABS_API_KEY` - مفتاح ElevenLabs
- `REPLICATE_API_TOKEN` - مفتاح Replicate

### إعدادات قاعدة البيانات
```env
DATABASE_URL=postgresql://user:password@localhost/spring_studio
REDIS_URL=redis://localhost:6379
```

## 📚 الاستخدام

1. **إنشاء مشروع جديد**
2. **إدخال فكرة الفيديو**
3. **اختيار القالب والإعدادات**
4. **معاينة المحتوى المولد**
5. **تخصيص التصميم حسب الحاجة**
6. **إنتاج الفيديو النهائي**
7. **تحميل أو مشاركة النتيجة**

## 🤝 المساهمة

نرحب بالمساهمات! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) للمزيد من التفاصيل.

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- **البريد الإلكتروني**: <EMAIL>
- **الموقع الإلكتروني**: https://spring-studio.com
- **تويتر**: @SpringStudioAI

---

**تم تطويره بـ ❤️ لخدمة المحتوى العربي**
