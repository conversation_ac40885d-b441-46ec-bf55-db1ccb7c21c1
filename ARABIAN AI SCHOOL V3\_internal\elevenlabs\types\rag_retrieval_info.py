# This file was auto-generated by <PERSON>rn from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from .rag_chunk_metadata import RagChunkMetadata
from .embedding_model_enum import EmbeddingModelEnum
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic


class RagRetrievalInfo(UncheckedBaseModel):
    chunks: typing.List[RagChunkMetadata]
    embedding_model: EmbeddingModelEnum
    retrieval_query: str
    rag_latency_secs: float

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
