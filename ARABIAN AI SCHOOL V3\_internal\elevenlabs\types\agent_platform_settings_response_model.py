# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from .auth_settings import AuthSettings
import pydantic
from .evaluation_settings import EvaluationSettings
from .widget_config import WidgetConfig
from .literal_json_schema_property import LiteralJsonSchemaProperty
from .conversation_initiation_client_data_config_output import (
    ConversationInitiationClientDataConfigOutput,
)
from .agent_call_limits import AgentCallLimits
from .privacy_config import PrivacyConfig
from .agent_workspace_overrides_output import AgentWorkspaceOverridesOutput
from .safety_response_model import SafetyResponseModel
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class AgentPlatformSettingsResponseModel(UncheckedBaseModel):
    auth: typing.Optional[AuthSettings] = pydantic.Field(default=None)
    """
    Settings for authentication
    """

    evaluation: typing.Optional[EvaluationSettings] = pydantic.Field(default=None)
    """
    Settings for evaluation
    """

    widget: typing.Optional[WidgetConfig] = pydantic.Field(default=None)
    """
    Configuration for the widget
    """

    data_collection: typing.Optional[typing.Dict[str, LiteralJsonSchemaProperty]] = pydantic.Field(default=None)
    """
    Data collection settings
    """

    overrides: typing.Optional[ConversationInitiationClientDataConfigOutput] = pydantic.Field(default=None)
    """
    Additional overrides for the agent during conversation initiation
    """

    call_limits: typing.Optional[AgentCallLimits] = pydantic.Field(default=None)
    """
    Call limits for the agent
    """

    privacy: typing.Optional[PrivacyConfig] = pydantic.Field(default=None)
    """
    Privacy settings for the agent
    """

    workspace_overrides: typing.Optional[AgentWorkspaceOverridesOutput] = pydantic.Field(default=None)
    """
    Workspace overrides for the agent
    """

    safety: typing.Optional[SafetyResponseModel] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
