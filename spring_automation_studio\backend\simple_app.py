"""
ستوديو الربيع للأتمتة - تطبيق بسيط للاختبار
Spring Automation Studio - Simple Test Application
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import time
from pathlib import Path
import sys
import os

# إضافة مسار التطبيق
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# إنشاء المجلدات المطلوبة
Path("uploads").mkdir(exist_ok=True)
Path("outputs").mkdir(exist_ok=True)
Path("temp").mkdir(exist_ok=True)

# إنشاء تطبيق FastAPI
app = FastAPI(
    title="Spring Automation Studio",
    description="AI-powered content creation system",
    version="2.0.0"
)

# إعداد CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# إضافة الملفات الثابتة
app.mount("/outputs", StaticFiles(directory="outputs"), name="outputs")
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

@app.get("/")
async def root():
    """الصفحة الرئيسية"""
    return {
        "message": "🌸 مرحباً بك في ستوديو الربيع للأتمتة",
        "version": "2.0.0",
        "status": "running",
        "docs": "/docs",
        "features": [
            "إنتاج فيديوهات احترافية",
            "ذكاء اصطناعي متطور", 
            "دعم كامل للعربية",
            "واجهة سهلة الاستخدام"
        ]
    }

@app.get("/health")
async def health_check():
    """فحص صحة النظام"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "message": "النظام يعمل بشكل طبيعي"
    }

@app.get("/api/test")
async def test_api():
    """اختبار API"""
    return {
        "success": True,
        "message": "API يعمل بنجاح!",
        "data": {
            "server": "FastAPI",
            "language": "Python",
            "framework": "Spring Automation Studio"
        }
    }

# إضافة API endpoints
try:
    from app.api.endpoints import router
    app.include_router(router, prefix="/api")
except ImportError:
    print("Warning: Could not import API endpoints")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
