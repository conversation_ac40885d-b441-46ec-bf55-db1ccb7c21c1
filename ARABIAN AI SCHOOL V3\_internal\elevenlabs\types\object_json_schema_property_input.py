# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations
from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic
from ..core.pydantic_utilities import update_forward_refs


class ObjectJsonSchemaPropertyInput(UncheckedBaseModel):
    type: typing.Optional[typing.Literal["object"]] = None
    properties: typing.Optional[typing.Dict[str, "ObjectJsonSchemaPropertyInputPropertiesValue"]] = None
    required: typing.Optional[typing.List[str]] = None
    description: typing.Optional[str] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


from .object_json_schema_property_input_properties_value import (
    ObjectJsonSchemaPropertyInputPropertiesValue,
)  # noqa: E402

update_forward_refs(ObjectJsonSchemaPropertyInput)
