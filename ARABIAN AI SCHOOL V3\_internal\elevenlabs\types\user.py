# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import pydantic
from .subscription_response import SubscriptionResponse
import typing
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class User(UncheckedBaseModel):
    user_id: str = pydantic.Field()
    """
    The unique identifier of the user.
    """

    subscription: SubscriptionResponse = pydantic.Field()
    """
    Details of the user's subscription.
    """

    subscription_extras: typing.Optional[typing.Optional[typing.Any]] = pydantic.Field(default=None)
    """
    Optional additional details about the user's subscription.
    """

    is_new_user: bool = pydantic.Field()
    """
    Whether the user is new.
    """

    xi_api_key: typing.Optional[str] = pydantic.Field(default=None)
    """
    The API key of the user.
    """

    can_use_delayed_payment_methods: bool = pydantic.Field()
    """
    Whether the user can use delayed payment methods.
    """

    is_onboarding_completed: bool = pydantic.Field()
    """
    Whether the user's onboarding is completed.
    """

    is_onboarding_checklist_completed: bool = pydantic.Field()
    """
    Whether the user's onboarding checklist is completed.
    """

    first_name: typing.Optional[str] = pydantic.Field(default=None)
    """
    First name of the user.
    """

    is_api_key_hashed: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether the user's API key is hashed.
    """

    xi_api_key_preview: typing.Optional[str] = pydantic.Field(default=None)
    """
    The preview of the user's API key.
    """

    referral_link_code: typing.Optional[str] = pydantic.Field(default=None)
    """
    The referral link code of the user.
    """

    partnerstack_partner_default_link: typing.Optional[str] = pydantic.Field(default=None)
    """
    The Partnerstack partner default link of the user.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
