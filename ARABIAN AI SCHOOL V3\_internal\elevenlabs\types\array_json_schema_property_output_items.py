# This file was auto-generated by Fern from our API Definition.

from __future__ import annotations
import typing
from .literal_json_schema_property import LiteralJsonSchemaProperty
import typing

if typing.TYPE_CHECKING:
    from .object_json_schema_property_output import ObjectJsonSchemaPropertyOutput
    from .array_json_schema_property_output import ArrayJsonSchemaPropertyOutput
ArrayJsonSchemaPropertyOutputItems = typing.Union[
    LiteralJsonSchemaProperty,
    "ObjectJsonSchemaPropertyOutput",
    "ArrayJsonSchemaPropertyOutput",
]
