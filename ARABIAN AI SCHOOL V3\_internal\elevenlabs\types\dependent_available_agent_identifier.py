# This file was auto-generated by <PERSON>rn from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
from .dependent_available_agent_identifier_access_level import (
    DependentAvailableAgentIdentifierAccessLevel,
)
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import typing
import pydantic


class DependentAvailableAgentIdentifier(UncheckedBaseModel):
    id: str
    name: str
    created_at_unix_secs: int
    access_level: DependentAvailableAgentIdentifierAccessLevel

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
