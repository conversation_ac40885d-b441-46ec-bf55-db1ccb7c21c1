# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import pydantic
import typing
from .telephony_provider import TelephonyProvider
from .sip_trunk_credentials import SipTrunkCredentials
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class CreateSipTrunkPhoneNumberRequest(UncheckedBaseModel):
    """
    SIP trunk phone number request

    Includes termination URI and optional digest authentication credentials.
    If credentials are provided, both username and password must be included.
    If credentials are not provided, ACL authentication is assumed. (user needs to add our ips in their settings)
    """

    phone_number: str = pydantic.Field()
    """
    Phone number
    """

    provider: typing.Optional[TelephonyProvider] = None
    label: str = pydantic.Field()
    """
    Label for the phone number
    """

    termination_uri: str = pydantic.Field()
    """
    SIP trunk termination URI
    """

    credentials: typing.Optional[SipTrunkCredentials] = pydantic.Field(default=None)
    """
    Optional digest authentication credentials (username/password). If not provided, ACL authentication is assumed.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
