# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations
from ..core.unchecked_base_model import UncheckedBaseModel
from .array_json_schema_property_output import ArrayJsonSchemaPropertyOutput
from .object_json_schema_property_output import ObjectJsonSchemaPropertyOutput
import typing
from .dynamic_variables_config import DynamicVariablesConfig
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic
from .webhook_tool_api_schema_config_output import WebhookToolApiSchemaConfigOutput
import typing_extensions
from ..core.unchecked_base_model import UnionMetadata


class PromptAgentOutputToolsItem_Client(UncheckedBaseModel):
    """
    The type of tool
    """

    type: typing.Literal["client"] = "client"
    name: str
    description: str
    parameters: typing.Optional[ObjectJsonSchemaPropertyOutput] = None
    expects_response: typing.Optional[bool] = None
    response_timeout_secs: typing.Optional[int] = None
    dynamic_variables: typing.Optional[DynamicVariablesConfig] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class PromptAgentOutputToolsItem_System(UncheckedBaseModel):
    """
    The type of tool
    """

    type: typing.Literal["system"] = "system"
    name: str
    description: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class PromptAgentOutputToolsItem_Webhook(UncheckedBaseModel):
    """
    The type of tool
    """

    type: typing.Literal["webhook"] = "webhook"
    name: str
    description: str
    api_schema: WebhookToolApiSchemaConfigOutput
    dynamic_variables: typing.Optional[DynamicVariablesConfig] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


PromptAgentOutputToolsItem = typing_extensions.Annotated[
    typing.Union[
        PromptAgentOutputToolsItem_Client,
        PromptAgentOutputToolsItem_System,
        PromptAgentOutputToolsItem_Webhook,
    ],
    UnionMetadata(discriminant="type"),
]
