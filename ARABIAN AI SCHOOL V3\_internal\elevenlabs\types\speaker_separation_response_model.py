# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
from .speaker_separation_response_model_status import (
    SpeakerSeparationResponseModelStatus,
)
import typing
from .speaker_response_model import SpeakerResponseModel
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic


class SpeakerSeparationResponseModel(UncheckedBaseModel):
    voice_id: str
    sample_id: str
    status: SpeakerSeparationResponseModelStatus
    speakers: typing.Optional[typing.Dict[str, typing.Optional[SpeakerResponseModel]]] = None
    selected_speaker_ids: typing.Optional[typing.List[str]] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
