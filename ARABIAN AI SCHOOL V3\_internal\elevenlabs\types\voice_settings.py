# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class VoiceSettings(UncheckedBaseModel):
    stability: typing.Optional[float] = pydantic.Field(default=None)
    """
    Determines how stable the voice is and the randomness between each generation. Lower values introduce broader emotional range for the voice. Higher values can result in a monotonous voice with limited emotion.
    """

    similarity_boost: typing.Optional[float] = pydantic.Field(default=None)
    """
    Determines how closely the AI should adhere to the original voice when attempting to replicate it.
    """

    style: typing.Optional[float] = pydantic.Field(default=None)
    """
    Determines the style exaggeration of the voice. This setting attempts to amplify the style of the original speaker. It does consume additional computational resources and might increase latency if set to anything other than 0.
    """

    use_speaker_boost: typing.Optional[bool] = pydantic.Field(default=None)
    """
    This setting boosts the similarity to the original speaker. Using this setting requires a slightly higher computational load, which in turn increases latency.
    """

    speed: typing.Optional[float] = pydantic.Field(default=None)
    """
    Controls the speed of the generated speech. Values range from 0.7 to 1.2, with 1.0 being the default speed. Lower values create slower, more deliberate speech while higher values produce faster-paced speech. Extreme values can impact the quality of the generated speech.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
