# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
from .array_json_schema_property_input import ArrayJsonSchemaPropertyInput
from .object_json_schema_property_input import ObjectJsonSchemaPropertyInput
import typing
from .asr_conversational_config import AsrConversationalConfig
import pydantic
from .turn_config import TurnConfig
from .tts_conversational_config import TtsConversationalConfig
from .conversation_config import ConversationConfig
from .language_preset_input import LanguagePresetInput
from .agent_config_api_model_input import AgentConfigApiModelInput
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class ConversationalConfigApiModelInput(UncheckedBaseModel):
    asr: typing.Optional[AsrConversationalConfig] = pydantic.Field(default=None)
    """
    Configuration for conversational transcription
    """

    turn: typing.Optional[TurnConfig] = pydantic.Field(default=None)
    """
    Configuration for turn detection
    """

    tts: typing.Optional[TtsConversationalConfig] = pydantic.Field(default=None)
    """
    Configuration for conversational text to speech
    """

    conversation: typing.Optional[ConversationConfig] = pydantic.Field(default=None)
    """
    Configuration for conversational events
    """

    language_presets: typing.Optional[typing.Dict[str, LanguagePresetInput]] = pydantic.Field(default=None)
    """
    Language presets for conversations
    """

    agent: typing.Optional[AgentConfigApiModelInput] = pydantic.Field(default=None)
    """
    Agent specific configuration
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
