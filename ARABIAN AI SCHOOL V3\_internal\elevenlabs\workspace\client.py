# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from ..core.client_wrapper import Sync<PERSON>lient<PERSON>rapper
from ..core.request_options import RequestOptions
from ..types.workspace_group_by_name_response_model import (
    WorkspaceGroupByNameResponseModel,
)
from ..core.unchecked_base_model import construct_type
from ..errors.unprocessable_entity_error import UnprocessableEntityError
from ..types.http_validation_error import HttpValidationError
from json.decoder import JSONDecodeError
from ..core.api_error import ApiError
from ..types.delete_workspace_group_member_response_model import (
    DeleteWorkspaceGroupMemberResponseModel,
)
from ..core.jsonable_encoder import jsonable_encoder
from ..types.add_workspace_group_member_response_model import (
    AddWorkspaceGroupMemberResponseModel,
)
from ..types.add_workspace_invite_response_model import AddWorkspaceInviteResponseModel
from ..types.delete_workspace_invite_response_model import (
    DeleteWorkspaceInviteResponseModel,
)
from .types.body_update_member_v_1_workspace_members_post_workspace_role import (
    BodyUpdateMemberV1WorkspaceMembersPostWorkspaceRole,
)
from ..types.update_workspace_member_response_model import (
    UpdateWorkspaceMemberResponseModel,
)
from ..types.workspace_resource_type import WorkspaceResourceType
from ..types.resource_metadata_response_model import ResourceMetadataResponseModel
from .types.body_share_workspace_resource_v_1_workspace_resources_resource_id_share_post_role import (
    BodyShareWorkspaceResourceV1WorkspaceResourcesResourceIdSharePostRole,
)
from ..core.client_wrapper import AsyncClientWrapper

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class WorkspaceClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def search_user_groups(
        self, *, name: str, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.List[WorkspaceGroupByNameResponseModel]:
        """
        Searches for user groups in the workspace. Multiple or no groups may be returned.

        Parameters
        ----------
        name : str
            Name of the target group.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[WorkspaceGroupByNameResponseModel]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.workspace.search_user_groups(
            name="name",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/workspace/groups/search",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            params={
                "name": name,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    typing.List[WorkspaceGroupByNameResponseModel],
                    construct_type(
                        type_=typing.List[WorkspaceGroupByNameResponseModel],  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def delete_member_from_user_group(
        self,
        group_id: str,
        *,
        email: str,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> DeleteWorkspaceGroupMemberResponseModel:
        """
        Removes a member from the specified group. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        group_id : str
            The ID of the target group.

        email : str
            The email of the target workspace member.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteWorkspaceGroupMemberResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.workspace.delete_member_from_user_group(
            group_id="group_id",
            email="email",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/workspace/groups/{jsonable_encoder(group_id)}/members/remove",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "email": email,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    DeleteWorkspaceGroupMemberResponseModel,
                    construct_type(
                        type_=DeleteWorkspaceGroupMemberResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def add_member_to_user_group(
        self,
        group_id: str,
        *,
        email: str,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddWorkspaceGroupMemberResponseModel:
        """
        Adds a member of your workspace to the specified group. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        group_id : str
            The ID of the target group.

        email : str
            The email of the target workspace member.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddWorkspaceGroupMemberResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.workspace.add_member_to_user_group(
            group_id="group_id",
            email="email",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/workspace/groups/{jsonable_encoder(group_id)}/members",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "email": email,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    AddWorkspaceGroupMemberResponseModel,
                    construct_type(
                        type_=AddWorkspaceGroupMemberResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def invite_user(
        self,
        *,
        email: str,
        group_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddWorkspaceInviteResponseModel:
        """
        Sends an email invitation to join your workspace to the provided email. If the user doesn't have an account they will be prompted to create one. If the user accepts this invite they will be added as a user to your workspace and your subscription using one of your seats. This endpoint may only be called by workspace administrators. If the user is already in the workspace a 400 error will be returned.

        Parameters
        ----------
        email : str
            The email of the customer

        group_ids : typing.Optional[typing.Sequence[str]]
            The group ids of the user

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddWorkspaceInviteResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.workspace.invite_user(
            email="<EMAIL>",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/workspace/invites/add",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "email": email,
                "group_ids": group_ids,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    AddWorkspaceInviteResponseModel,
                    construct_type(
                        type_=AddWorkspaceInviteResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def invite_multiple_users(
        self,
        *,
        emails: typing.Sequence[str],
        group_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddWorkspaceInviteResponseModel:
        """
        Sends email invitations to join your workspace to the provided emails. Requires all email addresses to be part of a verified domain. If the users don't have an account they will be prompted to create one. If the users accept these invites they will be added as users to your workspace and your subscription using one of your seats. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        emails : typing.Sequence[str]
            The email of the customer

        group_ids : typing.Optional[typing.Sequence[str]]
            The group ids of the user

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddWorkspaceInviteResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.workspace.invite_multiple_users(
            emails=["emails"],
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/workspace/invites/add-bulk",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "emails": emails,
                "group_ids": group_ids,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    AddWorkspaceInviteResponseModel,
                    construct_type(
                        type_=AddWorkspaceInviteResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def delete_existing_invitation(
        self, *, email: str, request_options: typing.Optional[RequestOptions] = None
    ) -> DeleteWorkspaceInviteResponseModel:
        """
        Invalidates an existing email invitation. The invitation will still show up in the inbox it has been delivered to, but activating it to join the workspace won't work. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        email : str
            The email of the customer

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteWorkspaceInviteResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.workspace.delete_existing_invitation(
            email="<EMAIL>",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/workspace/invites",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            json={
                "email": email,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    DeleteWorkspaceInviteResponseModel,
                    construct_type(
                        type_=DeleteWorkspaceInviteResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def update_member(
        self,
        *,
        email: str,
        is_locked: typing.Optional[bool] = OMIT,
        workspace_role: typing.Optional[BodyUpdateMemberV1WorkspaceMembersPostWorkspaceRole] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> UpdateWorkspaceMemberResponseModel:
        """
        Updates attributes of a workspace member. Apart from the email identifier, all parameters will remain unchanged unless specified. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        email : str
            Email of the target user.

        is_locked : typing.Optional[bool]
            Whether to lock or unlock the user account.

        workspace_role : typing.Optional[BodyUpdateMemberV1WorkspaceMembersPostWorkspaceRole]
            Role dictating permissions in the workspace.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        UpdateWorkspaceMemberResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.workspace.update_member(
            email="email",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/workspace/members",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "email": email,
                "is_locked": is_locked,
                "workspace_role": workspace_role,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    UpdateWorkspaceMemberResponseModel,
                    construct_type(
                        type_=UpdateWorkspaceMemberResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_resource(
        self,
        resource_id: str,
        *,
        resource_type: WorkspaceResourceType,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ResourceMetadataResponseModel:
        """
        Gets the metadata of a resource by ID.

        Parameters
        ----------
        resource_id : str
            The ID of the target resource.

        resource_type : WorkspaceResourceType
            Resource type of the target resource.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ResourceMetadataResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.workspace.get_resource(
            resource_id="resource_id",
            resource_type="voice",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/workspace/resources/{jsonable_encoder(resource_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            params={
                "resource_type": resource_type,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    ResourceMetadataResponseModel,
                    construct_type(
                        type_=ResourceMetadataResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def share_workspace_resource(
        self,
        resource_id: str,
        *,
        role: BodyShareWorkspaceResourceV1WorkspaceResourcesResourceIdSharePostRole,
        resource_type: WorkspaceResourceType,
        user_email: typing.Optional[str] = OMIT,
        group_id: typing.Optional[str] = OMIT,
        workspace_api_key_id: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Grants a role on a workspace resource to a user or a group. It overrides any existing role this user/group/workspace api key has on the resource. To target a user, pass only the user email. The user must be in your workspace. To target a group, pass only the group id. To target a workspace api key, pass the api key id. You must have admin access to the resource to share it.

        Parameters
        ----------
        resource_id : str
            The ID of the target resource.

        role : BodyShareWorkspaceResourceV1WorkspaceResourcesResourceIdSharePostRole
            Role to update the target principal with.

        resource_type : WorkspaceResourceType
            Resource type of the target resource.

        user_email : typing.Optional[str]
            The email of the user

        group_id : typing.Optional[str]
            The ID of the target group. To target the permissions principals have by default on this resource, use the value 'default'.

        workspace_api_key_id : typing.Optional[str]
            The ID of the target workspace API key. This isn't the same as the key itself that would you pass in the header for authentication. Workspace admins can find this in the workspace settings UI.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.workspace.share_workspace_resource(
            resource_id="resource_id",
            role="admin",
            resource_type="voice",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/workspace/resources/{jsonable_encoder(resource_id)}/share",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "role": role,
                "resource_type": resource_type,
                "user_email": user_email,
                "group_id": group_id,
                "workspace_api_key_id": workspace_api_key_id,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    typing.Optional[typing.Any],
                    construct_type(
                        type_=typing.Optional[typing.Any],  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def unshare_workspace_resource(
        self,
        resource_id: str,
        *,
        resource_type: WorkspaceResourceType,
        user_email: typing.Optional[str] = OMIT,
        group_id: typing.Optional[str] = OMIT,
        workspace_api_key_id: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Removes any existing role on a workspace resource from a user or a group. To target a user, pass only the user email. The user must be in your workspace. To target a group, pass only the group id. To target a workspace api key, pass the api key id. You must have admin access to the resource to unshare it. You cannot remove permissions from the user who created the resource.

        Parameters
        ----------
        resource_id : str
            The ID of the target resource.

        resource_type : WorkspaceResourceType
            Resource type of the target resource.

        user_email : typing.Optional[str]
            The email of the user

        group_id : typing.Optional[str]
            The ID of the target group. To target the permissions principals have by default on this resource, use the value 'default'.

        workspace_api_key_id : typing.Optional[str]
            The ID of the target workspace API key. This isn't the same as the key itself that would you pass in the header for authentication. Workspace admins can find this in the workspace settings UI.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.workspace.unshare_workspace_resource(
            resource_id="resource_id",
            resource_type="voice",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/workspace/resources/{jsonable_encoder(resource_id)}/unshare",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "resource_type": resource_type,
                "user_email": user_email,
                "group_id": group_id,
                "workspace_api_key_id": workspace_api_key_id,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    typing.Optional[typing.Any],
                    construct_type(
                        type_=typing.Optional[typing.Any],  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)


class AsyncWorkspaceClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def search_user_groups(
        self, *, name: str, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.List[WorkspaceGroupByNameResponseModel]:
        """
        Searches for user groups in the workspace. Multiple or no groups may be returned.

        Parameters
        ----------
        name : str
            Name of the target group.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[WorkspaceGroupByNameResponseModel]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.workspace.search_user_groups(
                name="name",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/workspace/groups/search",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            params={
                "name": name,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    typing.List[WorkspaceGroupByNameResponseModel],
                    construct_type(
                        type_=typing.List[WorkspaceGroupByNameResponseModel],  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def delete_member_from_user_group(
        self,
        group_id: str,
        *,
        email: str,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> DeleteWorkspaceGroupMemberResponseModel:
        """
        Removes a member from the specified group. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        group_id : str
            The ID of the target group.

        email : str
            The email of the target workspace member.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteWorkspaceGroupMemberResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.workspace.delete_member_from_user_group(
                group_id="group_id",
                email="email",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/workspace/groups/{jsonable_encoder(group_id)}/members/remove",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "email": email,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    DeleteWorkspaceGroupMemberResponseModel,
                    construct_type(
                        type_=DeleteWorkspaceGroupMemberResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def add_member_to_user_group(
        self,
        group_id: str,
        *,
        email: str,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddWorkspaceGroupMemberResponseModel:
        """
        Adds a member of your workspace to the specified group. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        group_id : str
            The ID of the target group.

        email : str
            The email of the target workspace member.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddWorkspaceGroupMemberResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.workspace.add_member_to_user_group(
                group_id="group_id",
                email="email",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/workspace/groups/{jsonable_encoder(group_id)}/members",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "email": email,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    AddWorkspaceGroupMemberResponseModel,
                    construct_type(
                        type_=AddWorkspaceGroupMemberResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def invite_user(
        self,
        *,
        email: str,
        group_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddWorkspaceInviteResponseModel:
        """
        Sends an email invitation to join your workspace to the provided email. If the user doesn't have an account they will be prompted to create one. If the user accepts this invite they will be added as a user to your workspace and your subscription using one of your seats. This endpoint may only be called by workspace administrators. If the user is already in the workspace a 400 error will be returned.

        Parameters
        ----------
        email : str
            The email of the customer

        group_ids : typing.Optional[typing.Sequence[str]]
            The group ids of the user

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddWorkspaceInviteResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.workspace.invite_user(
                email="<EMAIL>",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/workspace/invites/add",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "email": email,
                "group_ids": group_ids,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    AddWorkspaceInviteResponseModel,
                    construct_type(
                        type_=AddWorkspaceInviteResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def invite_multiple_users(
        self,
        *,
        emails: typing.Sequence[str],
        group_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddWorkspaceInviteResponseModel:
        """
        Sends email invitations to join your workspace to the provided emails. Requires all email addresses to be part of a verified domain. If the users don't have an account they will be prompted to create one. If the users accept these invites they will be added as users to your workspace and your subscription using one of your seats. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        emails : typing.Sequence[str]
            The email of the customer

        group_ids : typing.Optional[typing.Sequence[str]]
            The group ids of the user

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddWorkspaceInviteResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.workspace.invite_multiple_users(
                emails=["emails"],
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/workspace/invites/add-bulk",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "emails": emails,
                "group_ids": group_ids,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    AddWorkspaceInviteResponseModel,
                    construct_type(
                        type_=AddWorkspaceInviteResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def delete_existing_invitation(
        self, *, email: str, request_options: typing.Optional[RequestOptions] = None
    ) -> DeleteWorkspaceInviteResponseModel:
        """
        Invalidates an existing email invitation. The invitation will still show up in the inbox it has been delivered to, but activating it to join the workspace won't work. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        email : str
            The email of the customer

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteWorkspaceInviteResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.workspace.delete_existing_invitation(
                email="<EMAIL>",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/workspace/invites",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            json={
                "email": email,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    DeleteWorkspaceInviteResponseModel,
                    construct_type(
                        type_=DeleteWorkspaceInviteResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def update_member(
        self,
        *,
        email: str,
        is_locked: typing.Optional[bool] = OMIT,
        workspace_role: typing.Optional[BodyUpdateMemberV1WorkspaceMembersPostWorkspaceRole] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> UpdateWorkspaceMemberResponseModel:
        """
        Updates attributes of a workspace member. Apart from the email identifier, all parameters will remain unchanged unless specified. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        email : str
            Email of the target user.

        is_locked : typing.Optional[bool]
            Whether to lock or unlock the user account.

        workspace_role : typing.Optional[BodyUpdateMemberV1WorkspaceMembersPostWorkspaceRole]
            Role dictating permissions in the workspace.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        UpdateWorkspaceMemberResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.workspace.update_member(
                email="email",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/workspace/members",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "email": email,
                "is_locked": is_locked,
                "workspace_role": workspace_role,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    UpdateWorkspaceMemberResponseModel,
                    construct_type(
                        type_=UpdateWorkspaceMemberResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_resource(
        self,
        resource_id: str,
        *,
        resource_type: WorkspaceResourceType,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ResourceMetadataResponseModel:
        """
        Gets the metadata of a resource by ID.

        Parameters
        ----------
        resource_id : str
            The ID of the target resource.

        resource_type : WorkspaceResourceType
            Resource type of the target resource.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ResourceMetadataResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.workspace.get_resource(
                resource_id="resource_id",
                resource_type="voice",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/workspace/resources/{jsonable_encoder(resource_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            params={
                "resource_type": resource_type,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    ResourceMetadataResponseModel,
                    construct_type(
                        type_=ResourceMetadataResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def share_workspace_resource(
        self,
        resource_id: str,
        *,
        role: BodyShareWorkspaceResourceV1WorkspaceResourcesResourceIdSharePostRole,
        resource_type: WorkspaceResourceType,
        user_email: typing.Optional[str] = OMIT,
        group_id: typing.Optional[str] = OMIT,
        workspace_api_key_id: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Grants a role on a workspace resource to a user or a group. It overrides any existing role this user/group/workspace api key has on the resource. To target a user, pass only the user email. The user must be in your workspace. To target a group, pass only the group id. To target a workspace api key, pass the api key id. You must have admin access to the resource to share it.

        Parameters
        ----------
        resource_id : str
            The ID of the target resource.

        role : BodyShareWorkspaceResourceV1WorkspaceResourcesResourceIdSharePostRole
            Role to update the target principal with.

        resource_type : WorkspaceResourceType
            Resource type of the target resource.

        user_email : typing.Optional[str]
            The email of the user

        group_id : typing.Optional[str]
            The ID of the target group. To target the permissions principals have by default on this resource, use the value 'default'.

        workspace_api_key_id : typing.Optional[str]
            The ID of the target workspace API key. This isn't the same as the key itself that would you pass in the header for authentication. Workspace admins can find this in the workspace settings UI.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.workspace.share_workspace_resource(
                resource_id="resource_id",
                role="admin",
                resource_type="voice",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/workspace/resources/{jsonable_encoder(resource_id)}/share",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "role": role,
                "resource_type": resource_type,
                "user_email": user_email,
                "group_id": group_id,
                "workspace_api_key_id": workspace_api_key_id,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    typing.Optional[typing.Any],
                    construct_type(
                        type_=typing.Optional[typing.Any],  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def unshare_workspace_resource(
        self,
        resource_id: str,
        *,
        resource_type: WorkspaceResourceType,
        user_email: typing.Optional[str] = OMIT,
        group_id: typing.Optional[str] = OMIT,
        workspace_api_key_id: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Removes any existing role on a workspace resource from a user or a group. To target a user, pass only the user email. The user must be in your workspace. To target a group, pass only the group id. To target a workspace api key, pass the api key id. You must have admin access to the resource to unshare it. You cannot remove permissions from the user who created the resource.

        Parameters
        ----------
        resource_id : str
            The ID of the target resource.

        resource_type : WorkspaceResourceType
            Resource type of the target resource.

        user_email : typing.Optional[str]
            The email of the user

        group_id : typing.Optional[str]
            The ID of the target group. To target the permissions principals have by default on this resource, use the value 'default'.

        workspace_api_key_id : typing.Optional[str]
            The ID of the target workspace API key. This isn't the same as the key itself that would you pass in the header for authentication. Workspace admins can find this in the workspace settings UI.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.workspace.unshare_workspace_resource(
                resource_id="resource_id",
                resource_type="voice",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/workspace/resources/{jsonable_encoder(resource_id)}/unshare",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "resource_type": resource_type,
                "user_email": user_email,
                "group_id": group_id,
                "workspace_api_key_id": workspace_api_key_id,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    typing.Optional[typing.Any],
                    construct_type(
                        type_=typing.Optional[typing.Any],  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)
