# This file was auto-generated by Fern from our API Definition.

from .types import (
    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem,
    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Alias,
    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Phoneme,
    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostWorkspaceAccess,
    PronunciationDictionaryAddFromFileRequestWorkspaceAccess,
    PronunciationDictionaryGetAllRequestSort,
    PronunciationDictionaryRule,
    PronunciationDictionaryRule_Alias,
    PronunciationDictionaryRule_Phoneme,
)

__all__ = [
    "BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem",
    "BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Alias",
    "BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Phoneme",
    "BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostWorkspaceAccess",
    "PronunciationDictionaryAddFromFileRequestWorkspaceAccess",
    "PronunciationDictionaryGetAllRequestSort",
    "PronunciationDictionaryRule",
    "PronunciationDictionaryRule_Alias",
    "PronunciationDictionaryRule_Phoneme",
]
