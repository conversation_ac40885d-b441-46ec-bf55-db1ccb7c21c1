# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from .get_knowledge_base_list_response_model_documents_item import (
    GetKnowledgeBaseListResponseModelDocumentsItem,
)
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic


class GetKnowledgeBaseListResponseModel(UncheckedBaseModel):
    documents: typing.List[GetKnowledgeBaseListResponseModelDocumentsItem]
    next_cursor: typing.Optional[str] = None
    has_more: bool

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
