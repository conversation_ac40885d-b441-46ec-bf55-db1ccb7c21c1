# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from ..core.client_wrapper import SyncClientWrapper
from .types.body_create_podcast_v_1_projects_podcast_create_post_mode import (
    BodyCreatePodcastV1ProjectsPodcastCreatePostMode,
)
from .types.body_create_podcast_v_1_projects_podcast_create_post_source import (
    BodyCreatePodcastV1ProjectsPodcastCreatePostSource,
)
from .types.body_create_podcast_v_1_projects_podcast_create_post_quality_preset import (
    BodyCreatePodcastV1ProjectsPodcastCreatePostQualityPreset,
)
from .types.body_create_podcast_v_1_projects_podcast_create_post_duration_scale import (
    BodyCreatePodcastV1ProjectsPodcastCreatePostDurationScale,
)
from ..core.request_options import RequestOptions
from ..types.podcast_project_response_model import PodcastProjectResponseModel
from ..core.serialization import convert_and_respect_annotation_metadata
from ..core.unchecked_base_model import construct_type
from ..errors.unprocessable_entity_error import UnprocessableEntityError
from ..types.http_validation_error import HttpValidationError
from json.decoder import J<PERSON><PERSON>ecodeError
from ..core.api_error import ApiError
from ..types.get_projects_response import GetProjectsResponse
from .types.add_project_v_1_projects_add_post_request_target_audience import (
    AddProjectV1ProjectsAddPostRequestTargetAudience,
)
from .types.add_project_v_1_projects_add_post_request_fiction import (
    AddProjectV1ProjectsAddPostRequestFiction,
)
from .types.add_project_v_1_projects_add_post_request_apply_text_normalization import (
    AddProjectV1ProjectsAddPostRequestApplyTextNormalization,
)
from .types.add_project_v_1_projects_add_post_request_source_type import (
    AddProjectV1ProjectsAddPostRequestSourceType,
)
from ..types.add_project_response_model import AddProjectResponseModel
from ..types.project_extended_response_model import ProjectExtendedResponseModel
from ..core.jsonable_encoder import jsonable_encoder
from ..types.edit_project_response_model import EditProjectResponseModel
from ..types.project_snapshots_response import ProjectSnapshotsResponse
from ..types.get_chapters_response import GetChaptersResponse
from ..types.chapter_with_content_response_model import ChapterWithContentResponseModel
from ..types.chapter_content_input_model import ChapterContentInputModel
from ..types.edit_chapter_response_model import EditChapterResponseModel
from ..types.add_chapter_response_model import AddChapterResponseModel
from ..types.chapter_snapshots_response import ChapterSnapshotsResponse
from ..types.pronunciation_dictionary_version_locator import (
    PronunciationDictionaryVersionLocator,
)
from ..core.client_wrapper import AsyncClientWrapper

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class ProjectsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def create_podcast(
        self,
        *,
        model_id: str,
        mode: BodyCreatePodcastV1ProjectsPodcastCreatePostMode,
        source: BodyCreatePodcastV1ProjectsPodcastCreatePostSource,
        quality_preset: typing.Optional[BodyCreatePodcastV1ProjectsPodcastCreatePostQualityPreset] = OMIT,
        duration_scale: typing.Optional[BodyCreatePodcastV1ProjectsPodcastCreatePostDurationScale] = OMIT,
        language: typing.Optional[str] = OMIT,
        highlights: typing.Optional[typing.Sequence[str]] = OMIT,
        callback_url: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> PodcastProjectResponseModel:
        """
        Create and auto-convert a podcast project. Currently, the LLM cost is covered by us but you will still be charged for the audio generation. In the future, you will be charged for both the LLM and audio generation costs.

        Parameters
        ----------
        model_id : str
            The ID of the model to be used for this Studio project, you can query GET /v1/models to list all available models.

        mode : BodyCreatePodcastV1ProjectsPodcastCreatePostMode
            The type of podcast to generate. Can be 'conversation', an interaction between two voices, or 'bulletin', a monologue.

        source : BodyCreatePodcastV1ProjectsPodcastCreatePostSource
            The source content for the Podcast.

        quality_preset : typing.Optional[BodyCreatePodcastV1ProjectsPodcastCreatePostQualityPreset]
            Output quality of the generated audio. Must be one of:
            standard - standard output format, 128kbps with 44.1kHz sample rate.
            high - high quality output format, 192kbps with 44.1kHz sample rate and major improvements on our side. Using this setting increases the credit cost by 20%.
            ultra - ultra quality output format, 192kbps with 44.1kHz sample rate and highest improvements on our side. Using this setting increases the credit cost by 50%.
            ultra lossless - ultra quality output format, 705.6kbps with 44.1kHz sample rate and highest improvements on our side in a fully lossless format. Using this setting increases the credit cost by 100%.

        duration_scale : typing.Optional[BodyCreatePodcastV1ProjectsPodcastCreatePostDurationScale]
            Duration of the generated podcast. Must be one of:
            short - produces podcasts shorter than 3 minutes.
            default - produces podcasts roughly between 3-7 minutes.
            long - prodces podcasts longer than 7 minutes.

        language : typing.Optional[str]
            An optional language of the Studio project. Two-letter language code (ISO 639-1).

        highlights : typing.Optional[typing.Sequence[str]]
            A brief summary or highlights of the Studio project's content, providing key points or themes. This should be between 10 and 70 characters.

        callback_url : typing.Optional[str]
            A url that will be called by our service when the Studio project is converted. Request will contain a json blob containing the status of the conversion

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        PodcastProjectResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import (
            ElevenLabs,
            PodcastConversationModeData,
            PodcastTextSource,
        )
        from elevenlabs.projects import (
            BodyCreatePodcastV1ProjectsPodcastCreatePostMode_Conversation,
        )

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.projects.create_podcast(
            model_id="21m00Tcm4TlvDq8ikWAM",
            mode=BodyCreatePodcastV1ProjectsPodcastCreatePostMode_Conversation(
                conversation=PodcastConversationModeData(
                    host_voice_id="aw1NgEzBg83R7vgmiJt6",
                    guest_voice_id="aw1NgEzBg83R7vgmiJt7",
                ),
            ),
            source=PodcastTextSource(
                text="This is a test podcast.",
            ),
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/projects/podcast/create",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "model_id": model_id,
                "mode": convert_and_respect_annotation_metadata(
                    object_=mode,
                    annotation=BodyCreatePodcastV1ProjectsPodcastCreatePostMode,
                    direction="write",
                ),
                "source": convert_and_respect_annotation_metadata(
                    object_=source,
                    annotation=BodyCreatePodcastV1ProjectsPodcastCreatePostSource,
                    direction="write",
                ),
                "quality_preset": quality_preset,
                "duration_scale": duration_scale,
                "language": language,
                "highlights": highlights,
                "callback_url": callback_url,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    PodcastProjectResponseModel,
                    construct_type(
                        type_=PodcastProjectResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_projects(self, *, request_options: typing.Optional[RequestOptions] = None) -> GetProjectsResponse:
        """
        Returns a list of your projects together and its metadata.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetProjectsResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.projects.get_projects()
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/projects",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetProjectsResponse,
                    construct_type(
                        type_=GetProjectsResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def add_project(
        self,
        *,
        name: str,
        default_title_voice_id: str,
        default_paragraph_voice_id: str,
        default_model_id: str,
        from_url: typing.Optional[str] = OMIT,
        from_document: typing.Optional[str] = OMIT,
        quality_preset: typing.Optional[str] = OMIT,
        title: typing.Optional[str] = OMIT,
        author: typing.Optional[str] = OMIT,
        description: typing.Optional[str] = OMIT,
        genres: typing.Optional[typing.List[str]] = OMIT,
        target_audience: typing.Optional[AddProjectV1ProjectsAddPostRequestTargetAudience] = OMIT,
        language: typing.Optional[str] = OMIT,
        content_type: typing.Optional[str] = OMIT,
        original_publication_date: typing.Optional[str] = OMIT,
        mature_content: typing.Optional[bool] = OMIT,
        isbn_number: typing.Optional[str] = OMIT,
        acx_volume_normalization: typing.Optional[bool] = OMIT,
        volume_normalization: typing.Optional[bool] = OMIT,
        pronunciation_dictionary_locators: typing.Optional[typing.List[str]] = OMIT,
        fiction: typing.Optional[AddProjectV1ProjectsAddPostRequestFiction] = OMIT,
        apply_text_normalization: typing.Optional[AddProjectV1ProjectsAddPostRequestApplyTextNormalization] = OMIT,
        auto_convert: typing.Optional[bool] = OMIT,
        auto_assign_voices: typing.Optional[bool] = OMIT,
        source_type: typing.Optional[AddProjectV1ProjectsAddPostRequestSourceType] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddProjectResponseModel:
        """
        Creates a new project, it can be either initialized as blank, from a document or from a URL.

        Parameters
        ----------
        name : str
            The name of the Studio project, used for identification only.

        default_title_voice_id : str
            The voice_id that corresponds to the default voice used for new titles.

        default_paragraph_voice_id : str
            The voice_id that corresponds to the default voice used for new paragraphs.

        default_model_id : str
            The ID of the model to be used for this Studio project, you can query GET /v1/models to list all available models.

        from_url : typing.Optional[str]
            An optional URL from which we will extract content to initialize the Studio project. If this is set, 'from_url' must be null. If neither 'from_url' or 'from_document' are provided we will initialize the Studio project as blank.

        from_document : typing.Optional[str]
            An optional .epub, .pdf, .txt or similar file can be provided. If provided, we will initialize the Studio project with its content. If this is set, 'from_url' must be null.  If neither 'from_url' or 'from_document' are provided we will initialize the Studio project as blank.

        quality_preset : typing.Optional[str]
            Output quality of the generated audio. Must be one of:
            standard - standard output format, 128kbps with 44.1kHz sample rate.
            high - high quality output format, 192kbps with 44.1kHz sample rate and major improvements on our side. Using this setting increases the credit cost by 20%.
            ultra - ultra quality output format, 192kbps with 44.1kHz sample rate and highest improvements on our side. Using this setting increases the credit cost by 50%.
            ultra lossless - ultra quality output format, 705.6kbps with 44.1kHz sample rate and highest improvements on our side in a fully lossless format. Using this setting increases the credit cost by 100%.

        title : typing.Optional[str]
            An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.

        author : typing.Optional[str]
            An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.

        description : typing.Optional[str]
            An optional description of the Studio project.

        genres : typing.Optional[typing.List[str]]
            An optional list of genres associated with the Studio project.

        target_audience : typing.Optional[AddProjectV1ProjectsAddPostRequestTargetAudience]
            An optional target audience of the Studio project.

        language : typing.Optional[str]
            An optional language of the Studio project. Two-letter language code (ISO 639-1).

        content_type : typing.Optional[str]
            An optional content type of the Studio project.

        original_publication_date : typing.Optional[str]
            An optional original publication date of the Studio project, in the format YYYY-MM-DD or YYYY.

        mature_content : typing.Optional[bool]
            An optional specification of whether this Studio project contains mature content.

        isbn_number : typing.Optional[str]
            An optional ISBN number of the Studio project you want to create, this will be added as metadata to the mp3 file on Studio project or chapter download.

        acx_volume_normalization : typing.Optional[bool]
            [Deprecated] When the Studio project is downloaded, should the returned audio have postprocessing in order to make it compliant with audiobook normalized volume requirements

        volume_normalization : typing.Optional[bool]
            When the Studio project is downloaded, should the returned audio have postprocessing in order to make it compliant with audiobook normalized volume requirements

        pronunciation_dictionary_locators : typing.Optional[typing.List[str]]
            A list of pronunciation dictionary locators (pronunciation_dictionary_id, version_id) encoded as a list of JSON strings for pronunciation dictionaries to be applied to the text. A list of json encoded strings is required as adding projects may occur through formData as opposed to jsonBody. To specify multiple dictionaries use multiple --form lines in your curl, such as --form 'pronunciation_dictionary_locators="{\"pronunciation_dictionary_id\":\"Vmd4Zor6fplcA7WrINey\",\"version_id\":\"hRPaxjlTdR7wFMhV4w0b\"}"' --form 'pronunciation_dictionary_locators="{\"pronunciation_dictionary_id\":\"JzWtcGQMJ6bnlWwyMo7e\",\"version_id\":\"lbmwxiLu4q6txYxgdZqn\"}"'. Note that multiple dictionaries are not currently supported by our UI which will only show the first.

        fiction : typing.Optional[AddProjectV1ProjectsAddPostRequestFiction]
            An optional specification of whether the content of this Studio project is fiction.

        apply_text_normalization : typing.Optional[AddProjectV1ProjectsAddPostRequestApplyTextNormalization]

                This parameter controls text normalization with four modes: 'auto', 'on', 'apply_english' and 'off'.
                When set to 'auto', the system will automatically decide whether to apply text normalization
                (e.g., spelling out numbers). With 'on', text normalization will always be applied, while
                with 'off', it will be skipped. 'apply_english' is the same as 'on' but will assume that text is in English.


        auto_convert : typing.Optional[bool]
            Whether to auto convert the Studio project to audio or not.

        auto_assign_voices : typing.Optional[bool]
            [Alpha Feature] Whether automatically assign voices to phrases in the create Project.

        source_type : typing.Optional[AddProjectV1ProjectsAddPostRequestSourceType]
            The type of Studio project to create.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddProjectResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.projects.add_project(
            name="name",
            default_title_voice_id="default_title_voice_id",
            default_paragraph_voice_id="default_paragraph_voice_id",
            default_model_id="default_model_id",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/projects/add",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            data={
                "name": name,
                "default_title_voice_id": default_title_voice_id,
                "default_paragraph_voice_id": default_paragraph_voice_id,
                "default_model_id": default_model_id,
                "from_url": from_url,
                "from_document": from_document,
                "quality_preset": quality_preset,
                "title": title,
                "author": author,
                "description": description,
                "genres": genres,
                "target_audience": target_audience,
                "language": language,
                "content_type": content_type,
                "original_publication_date": original_publication_date,
                "mature_content": mature_content,
                "isbn_number": isbn_number,
                "acx_volume_normalization": acx_volume_normalization,
                "volume_normalization": volume_normalization,
                "pronunciation_dictionary_locators": pronunciation_dictionary_locators,
                "fiction": fiction,
                "apply_text_normalization": apply_text_normalization,
                "auto_convert": auto_convert,
                "auto_assign_voices": auto_assign_voices,
                "source_type": source_type,
            },
            files={},
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    AddProjectResponseModel,
                    construct_type(
                        type_=AddProjectResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_project_by_id(
        self,
        project_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ProjectExtendedResponseModel:
        """
        Returns information about a specific project. This endpoint returns more detailed information about a project than `GET /v1/projects`.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ProjectExtendedResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.projects.get_project_by_id(
            project_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    ProjectExtendedResponseModel,
                    construct_type(
                        type_=ProjectExtendedResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def edit_basic_project_info(
        self,
        project_id: str,
        *,
        name: str,
        default_title_voice_id: str,
        default_paragraph_voice_id: str,
        title: typing.Optional[str] = OMIT,
        author: typing.Optional[str] = OMIT,
        isbn_number: typing.Optional[str] = OMIT,
        volume_normalization: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> EditProjectResponseModel:
        """
        Edits basic project info.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        name : str
            The name of the Studio project, used for identification only.

        default_title_voice_id : str
            The voice_id that corresponds to the default voice used for new titles.

        default_paragraph_voice_id : str
            The voice_id that corresponds to the default voice used for new paragraphs.

        title : typing.Optional[str]
            An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.

        author : typing.Optional[str]
            An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.

        isbn_number : typing.Optional[str]
            An optional ISBN number of the Studio project you want to create, this will be added as metadata to the mp3 file on Studio project or chapter download.

        volume_normalization : typing.Optional[bool]
            When the Studio project is downloaded, should the returned audio have postprocessing in order to make it compliant with audiobook normalized volume requirements

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        EditProjectResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.projects.edit_basic_project_info(
            project_id="21m00Tcm4TlvDq8ikWAM",
            name="Project 1",
            default_title_voice_id="21m00Tcm4TlvDq8ikWAM",
            default_paragraph_voice_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "name": name,
                "default_title_voice_id": default_title_voice_id,
                "default_paragraph_voice_id": default_paragraph_voice_id,
                "title": title,
                "author": author,
                "isbn_number": isbn_number,
                "volume_normalization": volume_normalization,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    EditProjectResponseModel,
                    construct_type(
                        type_=EditProjectResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def delete_project(
        self,
        project_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Deletes a project.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.projects.delete_project(
            project_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    typing.Optional[typing.Any],
                    construct_type(
                        type_=typing.Optional[typing.Any],  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def edit_project_content(
        self,
        project_id: str,
        *,
        from_url: typing.Optional[str] = OMIT,
        from_document: typing.Optional[str] = OMIT,
        auto_convert: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> EditProjectResponseModel:
        """
        Edits project content.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        from_url : typing.Optional[str]
            An optional URL from which we will extract content to initialize the Studio project. If this is set, 'from_url' must be null. If neither 'from_url' or 'from_document' are provided we will initialize the Studio project as blank.

        from_document : typing.Optional[str]
            An optional .epub, .pdf, .txt or similar file can be provided. If provided, we will initialize the Studio project with its content. If this is set, 'from_url' must be null.  If neither 'from_url' or 'from_document' are provided we will initialize the Studio project as blank.

        auto_convert : typing.Optional[bool]
            Whether to auto convert the Studio project to audio or not.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        EditProjectResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.projects.edit_project_content(
            project_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}/content",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            data={
                "from_url": from_url,
                "from_document": from_document,
                "auto_convert": auto_convert,
            },
            files={},
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    EditProjectResponseModel,
                    construct_type(
                        type_=EditProjectResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def convert_project(
        self,
        project_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Starts conversion of a project and all of its chapters.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.projects.convert_project(
            project_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}/convert",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    typing.Optional[typing.Any],
                    construct_type(
                        type_=typing.Optional[typing.Any],  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_project_snapshots(
        self,
        project_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ProjectSnapshotsResponse:
        """
        Gets the snapshots of a project.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ProjectSnapshotsResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.projects.get_project_snapshots(
            project_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}/snapshots",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    ProjectSnapshotsResponse,
                    construct_type(
                        type_=ProjectSnapshotsResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def stream_project_audio(
        self,
        project_id: str,
        project_snapshot_id: str,
        *,
        convert_to_mpeg: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Iterator[bytes]:
        """
        Stream the audio from a project snapshot.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        project_snapshot_id : str
            The ID of the Studio project snapshot.

        convert_to_mpeg : typing.Optional[bool]
            Whether to convert the audio to mpeg format.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Yields
        ------
        typing.Iterator[bytes]
            Successful Response
        """
        with self._client_wrapper.httpx_client.stream(
            f"v1/projects/{jsonable_encoder(project_id)}/snapshots/{jsonable_encoder(project_snapshot_id)}/stream",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "convert_to_mpeg": convert_to_mpeg,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        ) as _response:
            try:
                if 200 <= _response.status_code < 300:
                    _chunk_size = request_options.get("chunk_size", 1024) if request_options is not None else 1024
                    for _chunk in _response.iter_bytes(chunk_size=_chunk_size):
                        yield _chunk
                    return
                _response.read()
                if _response.status_code == 422:
                    raise UnprocessableEntityError(
                        typing.cast(
                            HttpValidationError,
                            construct_type(
                                type_=HttpValidationError,  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                _response_json = _response.json()
            except JSONDecodeError:
                raise ApiError(status_code=_response.status_code, body=_response.text)
            raise ApiError(status_code=_response.status_code, body=_response_json)

    def streams_archive_with_project_audio(
        self,
        project_id: str,
        project_snapshot_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> None:
        """
        Streams archive with project audio.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        project_snapshot_id : str
            The ID of the Studio project snapshot.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.projects.streams_archive_with_project_audio(
            project_id="21m00Tcm4TlvDq8ikWAM",
            project_snapshot_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}/snapshots/{jsonable_encoder(project_snapshot_id)}/archive",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_chapters(
        self,
        project_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetChaptersResponse:
        """
        Returns a list of your chapters for a project together and its metadata.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetChaptersResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.projects.get_chapters(
            project_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}/chapters",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetChaptersResponse,
                    construct_type(
                        type_=GetChaptersResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_chapter_by_id(
        self,
        project_id: str,
        chapter_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ChapterWithContentResponseModel:
        """
        Returns information about a specific chapter.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        chapter_id : str
            The ID of the chapter.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ChapterWithContentResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.projects.get_chapter_by_id(
            project_id="21m00Tcm4TlvDq8ikWAM",
            chapter_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}/chapters/{jsonable_encoder(chapter_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    ChapterWithContentResponseModel,
                    construct_type(
                        type_=ChapterWithContentResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def delete_chapter(
        self,
        project_id: str,
        chapter_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Deletes a chapter.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        chapter_id : str
            The ID of the chapter.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.projects.delete_chapter(
            project_id="21m00Tcm4TlvDq8ikWAM",
            chapter_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}/chapters/{jsonable_encoder(chapter_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    typing.Optional[typing.Any],
                    construct_type(
                        type_=typing.Optional[typing.Any],  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def edit_chapter(
        self,
        project_id: str,
        chapter_id: str,
        *,
        name: typing.Optional[str] = OMIT,
        content: typing.Optional[ChapterContentInputModel] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> EditChapterResponseModel:
        """
        Edits a chapter.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        chapter_id : str
            The ID of the chapter.

        name : typing.Optional[str]
            The name of the chapter, used for identification only.

        content : typing.Optional[ChapterContentInputModel]
            The chapter content to use.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        EditChapterResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.projects.edit_chapter(
            project_id="21m00Tcm4TlvDq8ikWAM",
            chapter_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}/chapters/{jsonable_encoder(chapter_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="PATCH",
            json={
                "name": name,
                "content": convert_and_respect_annotation_metadata(
                    object_=content,
                    annotation=ChapterContentInputModel,
                    direction="write",
                ),
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    EditChapterResponseModel,
                    construct_type(
                        type_=EditChapterResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def add_chapter_to_a_project(
        self,
        project_id: str,
        *,
        name: str,
        from_url: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddChapterResponseModel:
        """
        Creates a new chapter either as blank or from a URL.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        name : str
            The name of the chapter, used for identification only.

        from_url : typing.Optional[str]
            An optional URL from which we will extract content to initialize the Studio project. If this is set, 'from_url' must be null. If neither 'from_url' or 'from_document' are provided we will initialize the Studio project as blank.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddChapterResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.projects.add_chapter_to_a_project(
            project_id="21m00Tcm4TlvDq8ikWAM",
            name="Chapter 1",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}/chapters/add",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "name": name,
                "from_url": from_url,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    AddChapterResponseModel,
                    construct_type(
                        type_=AddChapterResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def convert_chapter(
        self,
        project_id: str,
        chapter_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Starts conversion of a specific chapter.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        chapter_id : str
            The ID of the chapter.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.projects.convert_chapter(
            project_id="21m00Tcm4TlvDq8ikWAM",
            chapter_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}/chapters/{jsonable_encoder(chapter_id)}/convert",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    typing.Optional[typing.Any],
                    construct_type(
                        type_=typing.Optional[typing.Any],  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def list_chapter_snapshots(
        self,
        project_id: str,
        chapter_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ChapterSnapshotsResponse:
        """
        Gets information about all the snapshots of a chapter. Each snapshot can be downloaded as audio. Whenever a chapter is converted a snapshot will automatically be created.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        chapter_id : str
            The ID of the chapter.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ChapterSnapshotsResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.projects.list_chapter_snapshots(
            project_id="21m00Tcm4TlvDq8ikWAM",
            chapter_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}/chapters/{jsonable_encoder(chapter_id)}/snapshots",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    ChapterSnapshotsResponse,
                    construct_type(
                        type_=ChapterSnapshotsResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def stream_chapter_audio(
        self,
        project_id: str,
        chapter_id: str,
        chapter_snapshot_id: str,
        *,
        convert_to_mpeg: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> None:
        """
        Stream the audio from a chapter snapshot. Use `GET /v1/projects/{project_id}/chapters/{chapter_id}/snapshots` to return the chapter snapshots of a chapter.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        chapter_id : str
            The ID of the chapter.

        chapter_snapshot_id : str
            The ID of the chapter snapshot.

        convert_to_mpeg : typing.Optional[bool]
            Whether to convert the audio to mpeg format.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.projects.stream_chapter_audio(
            project_id="21m00Tcm4TlvDq8ikWAM",
            chapter_id="21m00Tcm4TlvDq8ikWAM",
            chapter_snapshot_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}/chapters/{jsonable_encoder(chapter_id)}/snapshots/{jsonable_encoder(chapter_snapshot_id)}/stream",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "convert_to_mpeg": convert_to_mpeg,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def update_pronunciation_dictionaries(
        self,
        project_id: str,
        *,
        pronunciation_dictionary_locators: typing.Sequence[PronunciationDictionaryVersionLocator],
        invalidate_affected_text: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Updates the set of pronunciation dictionaries acting on a project. This will automatically mark text within this project as requiring reconverting where the new dictionary would apply or the old one no longer does.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        pronunciation_dictionary_locators : typing.Sequence[PronunciationDictionaryVersionLocator]
            A list of pronunciation dictionary locators (pronunciation_dictionary_id, version_id) encoded as a list of JSON strings for pronunciation dictionaries to be applied to the text. A list of json encoded strings is required as adding projects may occur through formData as opposed to jsonBody. To specify multiple dictionaries use multiple --form lines in your curl, such as --form 'pronunciation_dictionary_locators="{\"pronunciation_dictionary_id\":\"Vmd4Zor6fplcA7WrINey\",\"version_id\":\"hRPaxjlTdR7wFMhV4w0b\"}"' --form 'pronunciation_dictionary_locators="{\"pronunciation_dictionary_id\":\"JzWtcGQMJ6bnlWwyMo7e\",\"version_id\":\"lbmwxiLu4q6txYxgdZqn\"}"'. Note that multiple dictionaries are not currently supported by our UI which will only show the first.

        invalidate_affected_text : typing.Optional[bool]
            This will automatically mark text in this project for reconversion when the new dictionary applies or the old one no longer does.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs, PronunciationDictionaryVersionLocator

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.projects.update_pronunciation_dictionaries(
            project_id="21m00Tcm4TlvDq8ikWAM",
            pronunciation_dictionary_locators=[
                PronunciationDictionaryVersionLocator(
                    pronunciation_dictionary_id="pronunciation_dictionary_id",
                )
            ],
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}/update-pronunciation-dictionaries",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "pronunciation_dictionary_locators": convert_and_respect_annotation_metadata(
                    object_=pronunciation_dictionary_locators,
                    annotation=typing.Sequence[PronunciationDictionaryVersionLocator],
                    direction="write",
                ),
                "invalidate_affected_text": invalidate_affected_text,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    typing.Optional[typing.Any],
                    construct_type(
                        type_=typing.Optional[typing.Any],  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)


class AsyncProjectsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def create_podcast(
        self,
        *,
        model_id: str,
        mode: BodyCreatePodcastV1ProjectsPodcastCreatePostMode,
        source: BodyCreatePodcastV1ProjectsPodcastCreatePostSource,
        quality_preset: typing.Optional[BodyCreatePodcastV1ProjectsPodcastCreatePostQualityPreset] = OMIT,
        duration_scale: typing.Optional[BodyCreatePodcastV1ProjectsPodcastCreatePostDurationScale] = OMIT,
        language: typing.Optional[str] = OMIT,
        highlights: typing.Optional[typing.Sequence[str]] = OMIT,
        callback_url: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> PodcastProjectResponseModel:
        """
        Create and auto-convert a podcast project. Currently, the LLM cost is covered by us but you will still be charged for the audio generation. In the future, you will be charged for both the LLM and audio generation costs.

        Parameters
        ----------
        model_id : str
            The ID of the model to be used for this Studio project, you can query GET /v1/models to list all available models.

        mode : BodyCreatePodcastV1ProjectsPodcastCreatePostMode
            The type of podcast to generate. Can be 'conversation', an interaction between two voices, or 'bulletin', a monologue.

        source : BodyCreatePodcastV1ProjectsPodcastCreatePostSource
            The source content for the Podcast.

        quality_preset : typing.Optional[BodyCreatePodcastV1ProjectsPodcastCreatePostQualityPreset]
            Output quality of the generated audio. Must be one of:
            standard - standard output format, 128kbps with 44.1kHz sample rate.
            high - high quality output format, 192kbps with 44.1kHz sample rate and major improvements on our side. Using this setting increases the credit cost by 20%.
            ultra - ultra quality output format, 192kbps with 44.1kHz sample rate and highest improvements on our side. Using this setting increases the credit cost by 50%.
            ultra lossless - ultra quality output format, 705.6kbps with 44.1kHz sample rate and highest improvements on our side in a fully lossless format. Using this setting increases the credit cost by 100%.

        duration_scale : typing.Optional[BodyCreatePodcastV1ProjectsPodcastCreatePostDurationScale]
            Duration of the generated podcast. Must be one of:
            short - produces podcasts shorter than 3 minutes.
            default - produces podcasts roughly between 3-7 minutes.
            long - prodces podcasts longer than 7 minutes.

        language : typing.Optional[str]
            An optional language of the Studio project. Two-letter language code (ISO 639-1).

        highlights : typing.Optional[typing.Sequence[str]]
            A brief summary or highlights of the Studio project's content, providing key points or themes. This should be between 10 and 70 characters.

        callback_url : typing.Optional[str]
            A url that will be called by our service when the Studio project is converted. Request will contain a json blob containing the status of the conversion

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        PodcastProjectResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import (
            AsyncElevenLabs,
            PodcastConversationModeData,
            PodcastTextSource,
        )
        from elevenlabs.projects import (
            BodyCreatePodcastV1ProjectsPodcastCreatePostMode_Conversation,
        )

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.projects.create_podcast(
                model_id="21m00Tcm4TlvDq8ikWAM",
                mode=BodyCreatePodcastV1ProjectsPodcastCreatePostMode_Conversation(
                    conversation=PodcastConversationModeData(
                        host_voice_id="aw1NgEzBg83R7vgmiJt6",
                        guest_voice_id="aw1NgEzBg83R7vgmiJt7",
                    ),
                ),
                source=PodcastTextSource(
                    text="This is a test podcast.",
                ),
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/projects/podcast/create",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "model_id": model_id,
                "mode": convert_and_respect_annotation_metadata(
                    object_=mode,
                    annotation=BodyCreatePodcastV1ProjectsPodcastCreatePostMode,
                    direction="write",
                ),
                "source": convert_and_respect_annotation_metadata(
                    object_=source,
                    annotation=BodyCreatePodcastV1ProjectsPodcastCreatePostSource,
                    direction="write",
                ),
                "quality_preset": quality_preset,
                "duration_scale": duration_scale,
                "language": language,
                "highlights": highlights,
                "callback_url": callback_url,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    PodcastProjectResponseModel,
                    construct_type(
                        type_=PodcastProjectResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_projects(self, *, request_options: typing.Optional[RequestOptions] = None) -> GetProjectsResponse:
        """
        Returns a list of your projects together and its metadata.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetProjectsResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.projects.get_projects()


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/projects",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetProjectsResponse,
                    construct_type(
                        type_=GetProjectsResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def add_project(
        self,
        *,
        name: str,
        default_title_voice_id: str,
        default_paragraph_voice_id: str,
        default_model_id: str,
        from_url: typing.Optional[str] = OMIT,
        from_document: typing.Optional[str] = OMIT,
        quality_preset: typing.Optional[str] = OMIT,
        title: typing.Optional[str] = OMIT,
        author: typing.Optional[str] = OMIT,
        description: typing.Optional[str] = OMIT,
        genres: typing.Optional[typing.List[str]] = OMIT,
        target_audience: typing.Optional[AddProjectV1ProjectsAddPostRequestTargetAudience] = OMIT,
        language: typing.Optional[str] = OMIT,
        content_type: typing.Optional[str] = OMIT,
        original_publication_date: typing.Optional[str] = OMIT,
        mature_content: typing.Optional[bool] = OMIT,
        isbn_number: typing.Optional[str] = OMIT,
        acx_volume_normalization: typing.Optional[bool] = OMIT,
        volume_normalization: typing.Optional[bool] = OMIT,
        pronunciation_dictionary_locators: typing.Optional[typing.List[str]] = OMIT,
        fiction: typing.Optional[AddProjectV1ProjectsAddPostRequestFiction] = OMIT,
        apply_text_normalization: typing.Optional[AddProjectV1ProjectsAddPostRequestApplyTextNormalization] = OMIT,
        auto_convert: typing.Optional[bool] = OMIT,
        auto_assign_voices: typing.Optional[bool] = OMIT,
        source_type: typing.Optional[AddProjectV1ProjectsAddPostRequestSourceType] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddProjectResponseModel:
        """
        Creates a new project, it can be either initialized as blank, from a document or from a URL.

        Parameters
        ----------
        name : str
            The name of the Studio project, used for identification only.

        default_title_voice_id : str
            The voice_id that corresponds to the default voice used for new titles.

        default_paragraph_voice_id : str
            The voice_id that corresponds to the default voice used for new paragraphs.

        default_model_id : str
            The ID of the model to be used for this Studio project, you can query GET /v1/models to list all available models.

        from_url : typing.Optional[str]
            An optional URL from which we will extract content to initialize the Studio project. If this is set, 'from_url' must be null. If neither 'from_url' or 'from_document' are provided we will initialize the Studio project as blank.

        from_document : typing.Optional[str]
            An optional .epub, .pdf, .txt or similar file can be provided. If provided, we will initialize the Studio project with its content. If this is set, 'from_url' must be null.  If neither 'from_url' or 'from_document' are provided we will initialize the Studio project as blank.

        quality_preset : typing.Optional[str]
            Output quality of the generated audio. Must be one of:
            standard - standard output format, 128kbps with 44.1kHz sample rate.
            high - high quality output format, 192kbps with 44.1kHz sample rate and major improvements on our side. Using this setting increases the credit cost by 20%.
            ultra - ultra quality output format, 192kbps with 44.1kHz sample rate and highest improvements on our side. Using this setting increases the credit cost by 50%.
            ultra lossless - ultra quality output format, 705.6kbps with 44.1kHz sample rate and highest improvements on our side in a fully lossless format. Using this setting increases the credit cost by 100%.

        title : typing.Optional[str]
            An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.

        author : typing.Optional[str]
            An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.

        description : typing.Optional[str]
            An optional description of the Studio project.

        genres : typing.Optional[typing.List[str]]
            An optional list of genres associated with the Studio project.

        target_audience : typing.Optional[AddProjectV1ProjectsAddPostRequestTargetAudience]
            An optional target audience of the Studio project.

        language : typing.Optional[str]
            An optional language of the Studio project. Two-letter language code (ISO 639-1).

        content_type : typing.Optional[str]
            An optional content type of the Studio project.

        original_publication_date : typing.Optional[str]
            An optional original publication date of the Studio project, in the format YYYY-MM-DD or YYYY.

        mature_content : typing.Optional[bool]
            An optional specification of whether this Studio project contains mature content.

        isbn_number : typing.Optional[str]
            An optional ISBN number of the Studio project you want to create, this will be added as metadata to the mp3 file on Studio project or chapter download.

        acx_volume_normalization : typing.Optional[bool]
            [Deprecated] When the Studio project is downloaded, should the returned audio have postprocessing in order to make it compliant with audiobook normalized volume requirements

        volume_normalization : typing.Optional[bool]
            When the Studio project is downloaded, should the returned audio have postprocessing in order to make it compliant with audiobook normalized volume requirements

        pronunciation_dictionary_locators : typing.Optional[typing.List[str]]
            A list of pronunciation dictionary locators (pronunciation_dictionary_id, version_id) encoded as a list of JSON strings for pronunciation dictionaries to be applied to the text. A list of json encoded strings is required as adding projects may occur through formData as opposed to jsonBody. To specify multiple dictionaries use multiple --form lines in your curl, such as --form 'pronunciation_dictionary_locators="{\"pronunciation_dictionary_id\":\"Vmd4Zor6fplcA7WrINey\",\"version_id\":\"hRPaxjlTdR7wFMhV4w0b\"}"' --form 'pronunciation_dictionary_locators="{\"pronunciation_dictionary_id\":\"JzWtcGQMJ6bnlWwyMo7e\",\"version_id\":\"lbmwxiLu4q6txYxgdZqn\"}"'. Note that multiple dictionaries are not currently supported by our UI which will only show the first.

        fiction : typing.Optional[AddProjectV1ProjectsAddPostRequestFiction]
            An optional specification of whether the content of this Studio project is fiction.

        apply_text_normalization : typing.Optional[AddProjectV1ProjectsAddPostRequestApplyTextNormalization]

                This parameter controls text normalization with four modes: 'auto', 'on', 'apply_english' and 'off'.
                When set to 'auto', the system will automatically decide whether to apply text normalization
                (e.g., spelling out numbers). With 'on', text normalization will always be applied, while
                with 'off', it will be skipped. 'apply_english' is the same as 'on' but will assume that text is in English.


        auto_convert : typing.Optional[bool]
            Whether to auto convert the Studio project to audio or not.

        auto_assign_voices : typing.Optional[bool]
            [Alpha Feature] Whether automatically assign voices to phrases in the create Project.

        source_type : typing.Optional[AddProjectV1ProjectsAddPostRequestSourceType]
            The type of Studio project to create.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddProjectResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.projects.add_project(
                name="name",
                default_title_voice_id="default_title_voice_id",
                default_paragraph_voice_id="default_paragraph_voice_id",
                default_model_id="default_model_id",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/projects/add",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            data={
                "name": name,
                "default_title_voice_id": default_title_voice_id,
                "default_paragraph_voice_id": default_paragraph_voice_id,
                "default_model_id": default_model_id,
                "from_url": from_url,
                "from_document": from_document,
                "quality_preset": quality_preset,
                "title": title,
                "author": author,
                "description": description,
                "genres": genres,
                "target_audience": target_audience,
                "language": language,
                "content_type": content_type,
                "original_publication_date": original_publication_date,
                "mature_content": mature_content,
                "isbn_number": isbn_number,
                "acx_volume_normalization": acx_volume_normalization,
                "volume_normalization": volume_normalization,
                "pronunciation_dictionary_locators": pronunciation_dictionary_locators,
                "fiction": fiction,
                "apply_text_normalization": apply_text_normalization,
                "auto_convert": auto_convert,
                "auto_assign_voices": auto_assign_voices,
                "source_type": source_type,
            },
            files={},
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    AddProjectResponseModel,
                    construct_type(
                        type_=AddProjectResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_project_by_id(
        self,
        project_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ProjectExtendedResponseModel:
        """
        Returns information about a specific project. This endpoint returns more detailed information about a project than `GET /v1/projects`.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ProjectExtendedResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.projects.get_project_by_id(
                project_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    ProjectExtendedResponseModel,
                    construct_type(
                        type_=ProjectExtendedResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def edit_basic_project_info(
        self,
        project_id: str,
        *,
        name: str,
        default_title_voice_id: str,
        default_paragraph_voice_id: str,
        title: typing.Optional[str] = OMIT,
        author: typing.Optional[str] = OMIT,
        isbn_number: typing.Optional[str] = OMIT,
        volume_normalization: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> EditProjectResponseModel:
        """
        Edits basic project info.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        name : str
            The name of the Studio project, used for identification only.

        default_title_voice_id : str
            The voice_id that corresponds to the default voice used for new titles.

        default_paragraph_voice_id : str
            The voice_id that corresponds to the default voice used for new paragraphs.

        title : typing.Optional[str]
            An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.

        author : typing.Optional[str]
            An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.

        isbn_number : typing.Optional[str]
            An optional ISBN number of the Studio project you want to create, this will be added as metadata to the mp3 file on Studio project or chapter download.

        volume_normalization : typing.Optional[bool]
            When the Studio project is downloaded, should the returned audio have postprocessing in order to make it compliant with audiobook normalized volume requirements

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        EditProjectResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.projects.edit_basic_project_info(
                project_id="21m00Tcm4TlvDq8ikWAM",
                name="Project 1",
                default_title_voice_id="21m00Tcm4TlvDq8ikWAM",
                default_paragraph_voice_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "name": name,
                "default_title_voice_id": default_title_voice_id,
                "default_paragraph_voice_id": default_paragraph_voice_id,
                "title": title,
                "author": author,
                "isbn_number": isbn_number,
                "volume_normalization": volume_normalization,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    EditProjectResponseModel,
                    construct_type(
                        type_=EditProjectResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def delete_project(
        self,
        project_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Deletes a project.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.projects.delete_project(
                project_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    typing.Optional[typing.Any],
                    construct_type(
                        type_=typing.Optional[typing.Any],  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def edit_project_content(
        self,
        project_id: str,
        *,
        from_url: typing.Optional[str] = OMIT,
        from_document: typing.Optional[str] = OMIT,
        auto_convert: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> EditProjectResponseModel:
        """
        Edits project content.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        from_url : typing.Optional[str]
            An optional URL from which we will extract content to initialize the Studio project. If this is set, 'from_url' must be null. If neither 'from_url' or 'from_document' are provided we will initialize the Studio project as blank.

        from_document : typing.Optional[str]
            An optional .epub, .pdf, .txt or similar file can be provided. If provided, we will initialize the Studio project with its content. If this is set, 'from_url' must be null.  If neither 'from_url' or 'from_document' are provided we will initialize the Studio project as blank.

        auto_convert : typing.Optional[bool]
            Whether to auto convert the Studio project to audio or not.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        EditProjectResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.projects.edit_project_content(
                project_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}/content",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            data={
                "from_url": from_url,
                "from_document": from_document,
                "auto_convert": auto_convert,
            },
            files={},
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    EditProjectResponseModel,
                    construct_type(
                        type_=EditProjectResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def convert_project(
        self,
        project_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Starts conversion of a project and all of its chapters.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.projects.convert_project(
                project_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}/convert",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    typing.Optional[typing.Any],
                    construct_type(
                        type_=typing.Optional[typing.Any],  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_project_snapshots(
        self,
        project_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ProjectSnapshotsResponse:
        """
        Gets the snapshots of a project.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ProjectSnapshotsResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.projects.get_project_snapshots(
                project_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}/snapshots",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    ProjectSnapshotsResponse,
                    construct_type(
                        type_=ProjectSnapshotsResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def stream_project_audio(
        self,
        project_id: str,
        project_snapshot_id: str,
        *,
        convert_to_mpeg: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.AsyncIterator[bytes]:
        """
        Stream the audio from a project snapshot.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        project_snapshot_id : str
            The ID of the Studio project snapshot.

        convert_to_mpeg : typing.Optional[bool]
            Whether to convert the audio to mpeg format.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Yields
        ------
        typing.AsyncIterator[bytes]
            Successful Response
        """
        async with self._client_wrapper.httpx_client.stream(
            f"v1/projects/{jsonable_encoder(project_id)}/snapshots/{jsonable_encoder(project_snapshot_id)}/stream",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "convert_to_mpeg": convert_to_mpeg,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        ) as _response:
            try:
                if 200 <= _response.status_code < 300:
                    _chunk_size = request_options.get("chunk_size", 1024) if request_options is not None else 1024
                    async for _chunk in _response.aiter_bytes(chunk_size=_chunk_size):
                        yield _chunk
                    return
                await _response.aread()
                if _response.status_code == 422:
                    raise UnprocessableEntityError(
                        typing.cast(
                            HttpValidationError,
                            construct_type(
                                type_=HttpValidationError,  # type: ignore
                                object_=_response.json(),
                            ),
                        )
                    )
                _response_json = _response.json()
            except JSONDecodeError:
                raise ApiError(status_code=_response.status_code, body=_response.text)
            raise ApiError(status_code=_response.status_code, body=_response_json)

    async def streams_archive_with_project_audio(
        self,
        project_id: str,
        project_snapshot_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> None:
        """
        Streams archive with project audio.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        project_snapshot_id : str
            The ID of the Studio project snapshot.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.projects.streams_archive_with_project_audio(
                project_id="21m00Tcm4TlvDq8ikWAM",
                project_snapshot_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}/snapshots/{jsonable_encoder(project_snapshot_id)}/archive",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_chapters(
        self,
        project_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetChaptersResponse:
        """
        Returns a list of your chapters for a project together and its metadata.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetChaptersResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.projects.get_chapters(
                project_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}/chapters",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    GetChaptersResponse,
                    construct_type(
                        type_=GetChaptersResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_chapter_by_id(
        self,
        project_id: str,
        chapter_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ChapterWithContentResponseModel:
        """
        Returns information about a specific chapter.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        chapter_id : str
            The ID of the chapter.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ChapterWithContentResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.projects.get_chapter_by_id(
                project_id="21m00Tcm4TlvDq8ikWAM",
                chapter_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}/chapters/{jsonable_encoder(chapter_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    ChapterWithContentResponseModel,
                    construct_type(
                        type_=ChapterWithContentResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def delete_chapter(
        self,
        project_id: str,
        chapter_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Deletes a chapter.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        chapter_id : str
            The ID of the chapter.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.projects.delete_chapter(
                project_id="21m00Tcm4TlvDq8ikWAM",
                chapter_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}/chapters/{jsonable_encoder(chapter_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    typing.Optional[typing.Any],
                    construct_type(
                        type_=typing.Optional[typing.Any],  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def edit_chapter(
        self,
        project_id: str,
        chapter_id: str,
        *,
        name: typing.Optional[str] = OMIT,
        content: typing.Optional[ChapterContentInputModel] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> EditChapterResponseModel:
        """
        Edits a chapter.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        chapter_id : str
            The ID of the chapter.

        name : typing.Optional[str]
            The name of the chapter, used for identification only.

        content : typing.Optional[ChapterContentInputModel]
            The chapter content to use.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        EditChapterResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.projects.edit_chapter(
                project_id="21m00Tcm4TlvDq8ikWAM",
                chapter_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}/chapters/{jsonable_encoder(chapter_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="PATCH",
            json={
                "name": name,
                "content": convert_and_respect_annotation_metadata(
                    object_=content,
                    annotation=ChapterContentInputModel,
                    direction="write",
                ),
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    EditChapterResponseModel,
                    construct_type(
                        type_=EditChapterResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def add_chapter_to_a_project(
        self,
        project_id: str,
        *,
        name: str,
        from_url: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddChapterResponseModel:
        """
        Creates a new chapter either as blank or from a URL.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        name : str
            The name of the chapter, used for identification only.

        from_url : typing.Optional[str]
            An optional URL from which we will extract content to initialize the Studio project. If this is set, 'from_url' must be null. If neither 'from_url' or 'from_document' are provided we will initialize the Studio project as blank.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddChapterResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.projects.add_chapter_to_a_project(
                project_id="21m00Tcm4TlvDq8ikWAM",
                name="Chapter 1",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}/chapters/add",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "name": name,
                "from_url": from_url,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    AddChapterResponseModel,
                    construct_type(
                        type_=AddChapterResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def convert_chapter(
        self,
        project_id: str,
        chapter_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Starts conversion of a specific chapter.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        chapter_id : str
            The ID of the chapter.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.projects.convert_chapter(
                project_id="21m00Tcm4TlvDq8ikWAM",
                chapter_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}/chapters/{jsonable_encoder(chapter_id)}/convert",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    typing.Optional[typing.Any],
                    construct_type(
                        type_=typing.Optional[typing.Any],  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def list_chapter_snapshots(
        self,
        project_id: str,
        chapter_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ChapterSnapshotsResponse:
        """
        Gets information about all the snapshots of a chapter. Each snapshot can be downloaded as audio. Whenever a chapter is converted a snapshot will automatically be created.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        chapter_id : str
            The ID of the chapter.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ChapterSnapshotsResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.projects.list_chapter_snapshots(
                project_id="21m00Tcm4TlvDq8ikWAM",
                chapter_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}/chapters/{jsonable_encoder(chapter_id)}/snapshots",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    ChapterSnapshotsResponse,
                    construct_type(
                        type_=ChapterSnapshotsResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def stream_chapter_audio(
        self,
        project_id: str,
        chapter_id: str,
        chapter_snapshot_id: str,
        *,
        convert_to_mpeg: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> None:
        """
        Stream the audio from a chapter snapshot. Use `GET /v1/projects/{project_id}/chapters/{chapter_id}/snapshots` to return the chapter snapshots of a chapter.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        chapter_id : str
            The ID of the chapter.

        chapter_snapshot_id : str
            The ID of the chapter snapshot.

        convert_to_mpeg : typing.Optional[bool]
            Whether to convert the audio to mpeg format.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.projects.stream_chapter_audio(
                project_id="21m00Tcm4TlvDq8ikWAM",
                chapter_id="21m00Tcm4TlvDq8ikWAM",
                chapter_snapshot_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}/chapters/{jsonable_encoder(chapter_id)}/snapshots/{jsonable_encoder(chapter_snapshot_id)}/stream",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "convert_to_mpeg": convert_to_mpeg,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def update_pronunciation_dictionaries(
        self,
        project_id: str,
        *,
        pronunciation_dictionary_locators: typing.Sequence[PronunciationDictionaryVersionLocator],
        invalidate_affected_text: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Updates the set of pronunciation dictionaries acting on a project. This will automatically mark text within this project as requiring reconverting where the new dictionary would apply or the old one no longer does.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        pronunciation_dictionary_locators : typing.Sequence[PronunciationDictionaryVersionLocator]
            A list of pronunciation dictionary locators (pronunciation_dictionary_id, version_id) encoded as a list of JSON strings for pronunciation dictionaries to be applied to the text. A list of json encoded strings is required as adding projects may occur through formData as opposed to jsonBody. To specify multiple dictionaries use multiple --form lines in your curl, such as --form 'pronunciation_dictionary_locators="{\"pronunciation_dictionary_id\":\"Vmd4Zor6fplcA7WrINey\",\"version_id\":\"hRPaxjlTdR7wFMhV4w0b\"}"' --form 'pronunciation_dictionary_locators="{\"pronunciation_dictionary_id\":\"JzWtcGQMJ6bnlWwyMo7e\",\"version_id\":\"lbmwxiLu4q6txYxgdZqn\"}"'. Note that multiple dictionaries are not currently supported by our UI which will only show the first.

        invalidate_affected_text : typing.Optional[bool]
            This will automatically mark text in this project for reconversion when the new dictionary applies or the old one no longer does.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs, PronunciationDictionaryVersionLocator

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.projects.update_pronunciation_dictionaries(
                project_id="21m00Tcm4TlvDq8ikWAM",
                pronunciation_dictionary_locators=[
                    PronunciationDictionaryVersionLocator(
                        pronunciation_dictionary_id="pronunciation_dictionary_id",
                    )
                ],
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/projects/{jsonable_encoder(project_id)}/update-pronunciation-dictionaries",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "pronunciation_dictionary_locators": convert_and_respect_annotation_metadata(
                    object_=pronunciation_dictionary_locators,
                    annotation=typing.Sequence[PronunciationDictionaryVersionLocator],
                    direction="write",
                ),
                "invalidate_affected_text": invalidate_affected_text,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    typing.Optional[typing.Any],
                    construct_type(
                        type_=typing.Optional[typing.Any],  # type: ignore
                        object_=_response.json(),
                    ),
                )
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    )
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)
