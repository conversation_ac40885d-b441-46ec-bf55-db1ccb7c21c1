"""
نقاط النهاية للـ API
API Endpoints
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import Optional, Dict, Any, List
import logging
import asyncio
import uuid

from app.services.ai_service import ai_service
from app.services.video_service import video_service

logger = logging.getLogger(__name__)

router = APIRouter()


class VideoRequest(BaseModel):
    """طلب إنشاء فيديو"""
    topic: str
    style: Optional[str] = "educational"
    duration: Optional[int] = 30
    language: Optional[str] = "arabic"


class VideoResponse(BaseModel):
    """استجابة إنشاء الفيديو"""
    success: bool
    message: str
    video_id: Optional[str] = None
    status: Optional[str] = None
    data: Optional[Dict[str, Any]] = None


@router.get("/")
async def root():
    """الصفحة الرئيسية للـ API"""
    return {
        "message": "🌸 مرحباً بك في ستوديو الربيع للأتمتة API",
        "version": "2.0.0",
        "endpoints": {
            "create_video": "/api/create-video",
            "video_status": "/api/video/{video_id}",
            "test_ai": "/api/test-ai",
            "health": "/api/health"
        }
    }


@router.get("/health")
async def health_check():
    """فحص صحة النظام"""
    return {
        "status": "healthy",
        "message": "النظام يعمل بشكل طبيعي",
        "services": {
            "ai_service": "active",
            "video_service": "active"
        }
    }


@router.post("/test-ai", response_model=Dict[str, Any])
async def test_ai_services(topic: str = "الذكاء الاصطناعي"):
    """اختبار خدمات الذكاء الاصطناعي"""
    try:
        # اختبار توليد النص
        text_result = ai_service.generate_text_with_gemini(
            f"اكتب فقرة قصيرة عن {topic}"
        )
        
        if not text_result:
            text_result = ai_service.generate_text_with_openai(
                f"اكتب فقرة قصيرة عن {topic}"
            )
        
        # اختبار توليد وصف الصورة
        image_prompt = ai_service.generate_image_prompt(topic)
        
        return {
            "success": True,
            "message": "تم اختبار خدمات الذكاء الاصطناعي بنجاح",
            "results": {
                "text_generation": {
                    "success": text_result is not None,
                    "content": text_result[:100] + "..." if text_result else None
                },
                "image_prompt": {
                    "success": image_prompt is not None,
                    "content": image_prompt[:100] + "..." if image_prompt else None
                }
            }
        }
        
    except Exception as e:
        logger.error(f"Error testing AI services: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ في اختبار خدمات الذكاء الاصطناعي: {str(e)}")


@router.post("/create-video", response_model=VideoResponse)
async def create_video(request: VideoRequest, background_tasks: BackgroundTasks):
    """إنشاء فيديو جديد"""
    try:
        video_id = uuid.uuid4().hex
        
        # بدء المعالجة في الخلفية
        background_tasks.add_task(process_video_creation, video_id, request)
        
        return VideoResponse(
            success=True,
            message="تم بدء إنشاء الفيديو بنجاح",
            video_id=video_id,
            status="processing"
        )
        
    except Exception as e:
        logger.error(f"Error creating video: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ في إنشاء الفيديو: {str(e)}")


@router.get("/video/{video_id}")
async def get_video_status(video_id: str):
    """الحصول على حالة الفيديو"""
    try:
        # البحث عن ملف معلومات الفيديو
        info_file = video_service.output_dir / f"video_info_{video_id}.json"
        
        if info_file.exists():
            import json
            with open(info_file, 'r', encoding='utf-8') as f:
                video_info = json.load(f)
            
            return {
                "success": True,
                "video_id": video_id,
                "status": video_info.get("status", "unknown"),
                "data": video_info
            }
        else:
            return {
                "success": False,
                "message": "الفيديو غير موجود",
                "video_id": video_id,
                "status": "not_found"
            }
            
    except Exception as e:
        logger.error(f"Error getting video status: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ في الحصول على حالة الفيديو: {str(e)}")


@router.get("/videos")
async def list_videos():
    """قائمة بجميع الفيديوهات"""
    try:
        videos = []
        
        # البحث عن ملفات معلومات الفيديو
        for info_file in video_service.output_dir.glob("video_info_*.json"):
            try:
                import json
                with open(info_file, 'r', encoding='utf-8') as f:
                    video_info = json.load(f)
                    videos.append({
                        "id": video_info.get("id"),
                        "status": video_info.get("status"),
                        "created_at": video_info.get("created_at"),
                        "topic": video_info.get("script", {}).get("full_text", "")[:50] + "..."
                    })
            except Exception as e:
                logger.error(f"Error reading video info file {info_file}: {e}")
                continue
        
        return {
            "success": True,
            "message": f"تم العثور على {len(videos)} فيديو",
            "videos": videos
        }
        
    except Exception as e:
        logger.error(f"Error listing videos: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ في الحصول على قائمة الفيديوهات: {str(e)}")


async def process_video_creation(video_id: str, request: VideoRequest):
    """معالجة إنشاء الفيديو في الخلفية"""
    try:
        logger.info(f"Starting video creation for ID: {video_id}")
        
        # 1. إنشاء السكريبت
        script_data = ai_service.create_educational_script(request.topic)
        if not script_data:
            logger.error("Failed to create script")
            return
        
        # 2. توليد الصور
        image_paths = []
        
        # صورة رئيسية
        main_image_prompt = ai_service.generate_image_prompt(request.topic)
        if main_image_prompt:
            main_image = video_service.generate_image_with_pollinations(main_image_prompt)
            if main_image:
                image_paths.append(main_image)
        
        # صورة نصية كبديل
        text_image = video_service.create_text_overlay_image(
            script_data["full_text"][:100] + "..." if len(script_data["full_text"]) > 100 else script_data["full_text"]
        )
        if text_image:
            image_paths.append(text_image)
        
        # 3. تحويل النص إلى كلام
        audio_path = None
        audio_data = ai_service.text_to_speech_elevenlabs(script_data["full_text"])
        if audio_data:
            audio_path = video_service.save_audio_file(audio_data)
        
        # 4. إنشاء معلومات الفيديو
        video_info = video_service.create_simple_video_info(
            script_data, image_paths, audio_path or ""
        )
        video_info["id"] = video_id
        video_info["status"] = "completed" if image_paths and audio_path else "partial"
        
        # حفظ معلومات الفيديو المحدثة
        import json
        info_file = video_service.output_dir / f"video_info_{video_id}.json"
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(video_info, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Video creation completed for ID: {video_id}")
        
    except Exception as e:
        logger.error(f"Error in video creation process: {e}")
        
        # حفظ معلومات الخطأ
        error_info = {
            "id": video_id,
            "status": "error",
            "error": str(e),
            "topic": request.topic
        }
        
        import json
        info_file = video_service.output_dir / f"video_info_{video_id}.json"
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(error_info, f, ensure_ascii=False, indent=2)
