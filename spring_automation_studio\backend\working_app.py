"""
Spring Automation Studio - Working Application
"""

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
import time
import logging
from pathlib import Path
import sys
import os
import uuid
import requests
from typing import Optional, Dict, Any

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create required directories
Path("uploads").mkdir(exist_ok=True)
Path("outputs").mkdir(exist_ok=True)
Path("temp").mkdir(exist_ok=True)

# Create FastAPI application
app = FastAPI(
    title="Spring Automation Studio",
    description="AI-powered content creation system",
    version="2.0.0",
)

# Setup CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")
app.mount("/outputs", StaticFiles(directory="outputs"), name="outputs")

# Request models
class VideoRequest(BaseModel):
    topic: str
    style: Optional[str] = "educational"
    duration: Optional[int] = 30

class VideoResponse(BaseModel):
    success: bool
    message: str
    video_id: Optional[str] = None
    status: Optional[str] = None

# Basic AI Service
class SimpleAIService:
    def __init__(self):
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.gemini_api_key = os.getenv("GEMINI_API_KEY")
    
    def generate_text(self, prompt: str) -> Optional[str]:
        """Generate text using available AI services"""
        # Try Gemini first (free tier available)
        if self.gemini_api_key:
            try:
                url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={self.gemini_api_key}"
                headers = {"Content-Type": "application/json"}
                data = {
                    "contents": [{"parts": [{"text": prompt}]}]
                }
                response = requests.post(url, headers=headers, json=data, timeout=30)
                if response.status_code == 200:
                    result = response.json()
                    if "candidates" in result and len(result["candidates"]) > 0:
                        return result["candidates"][0]["content"]["parts"][0]["text"]
            except Exception as e:
                logger.error(f"Gemini API error: {e}")
        
        # Fallback to OpenAI
        if self.openai_api_key:
            try:
                headers = {
                    "Authorization": f"Bearer {self.openai_api_key}",
                    "Content-Type": "application/json"
                }
                data = {
                    "model": "gpt-3.5-turbo",
                    "messages": [{"role": "user", "content": prompt}],
                    "max_tokens": 1000,
                    "temperature": 0.7
                }
                response = requests.post(
                    "https://api.openai.com/v1/chat/completions",
                    headers=headers, json=data, timeout=30
                )
                if response.status_code == 200:
                    result = response.json()
                    return result["choices"][0]["message"]["content"]
            except Exception as e:
                logger.error(f"OpenAI API error: {e}")
        
        # Fallback to demo content
        return f"هذا محتوى تجريبي عن {prompt}. يمكنك إضافة مفاتيح API للحصول على محتوى حقيقي من الذكاء الاصطناعي."

    def create_script(self, topic: str) -> Dict[str, Any]:
        """Create educational script"""
        prompt = f"""
        اكتب سكريبت تعليمي قصير عن الموضوع التالي: {topic}
        
        المتطلبات:
        - مدة 30-60 ثانية
        - لغة عربية واضحة
        - محتوى تعليمي مفيد
        - مناسب للفيديوهات القصيرة
        
        اكتب النص فقط بدون أي إضافات:
        """
        
        text = self.generate_text(prompt)
        if text:
            sentences = [s.strip() for s in text.split('.') if s.strip()]
            return {
                "full_text": text,
                "sentences": sentences,
                "duration_estimate": len(text.split()) * 0.5,
                "word_count": len(text.split())
            }
        return {
            "full_text": f"محتوى تعليمي عن {topic}",
            "sentences": [f"هذا محتوى تعليمي عن {topic}"],
            "duration_estimate": 30,
            "word_count": 10
        }

# Initialize services
ai_service = SimpleAIService()

# API Endpoints
@app.get("/")
async def root():
    """Main page"""
    return {
        "message": "🌸 Welcome to Spring Automation Studio",
        "version": "2.0.0",
        "status": "running",
        "docs": "/docs",
        "features": [
            "AI-powered content creation",
            "Arabic language support",
            "Video generation",
            "Easy to use API"
        ]
    }

@app.get("/health")
async def health_check():
    """System health check"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "message": "System is running normally",
        "services": {
            "api": "healthy",
            "ai_service": "active"
        }
    }

@app.post("/api/test-ai")
async def test_ai(topic: str = "الذكاء الاصطناعي"):
    """Test AI services"""
    try:
        script = ai_service.create_script(topic)
        return {
            "success": True,
            "message": "AI services tested successfully",
            "data": {
                "topic": topic,
                "script_preview": script["full_text"][:100] + "...",
                "word_count": script["word_count"],
                "estimated_duration": script["duration_estimate"]
            }
        }
    except Exception as e:
        logger.error(f"Error testing AI: {e}")
        raise HTTPException(status_code=500, detail=f"AI test failed: {str(e)}")

@app.post("/api/create-video", response_model=VideoResponse)
async def create_video(request: VideoRequest, background_tasks: BackgroundTasks):
    """Create a new video"""
    try:
        video_id = uuid.uuid4().hex
        background_tasks.add_task(process_video, video_id, request)
        
        return VideoResponse(
            success=True,
            message="Video creation started successfully",
            video_id=video_id,
            status="processing"
        )
    except Exception as e:
        logger.error(f"Error creating video: {e}")
        raise HTTPException(status_code=500, detail=f"Video creation failed: {str(e)}")

@app.get("/api/video/{video_id}")
async def get_video_status(video_id: str):
    """Get video status"""
    try:
        info_file = Path("outputs") / f"video_info_{video_id}.json"
        if info_file.exists():
            import json
            with open(info_file, 'r', encoding='utf-8') as f:
                video_info = json.load(f)
            return {
                "success": True,
                "video_id": video_id,
                "status": video_info.get("status", "unknown"),
                "data": video_info
            }
        else:
            return {
                "success": False,
                "message": "Video not found",
                "video_id": video_id,
                "status": "not_found"
            }
    except Exception as e:
        logger.error(f"Error getting video status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get video status: {str(e)}")

async def process_video(video_id: str, request: VideoRequest):
    """Process video creation in background"""
    try:
        logger.info(f"Starting video creation for ID: {video_id}")
        
        # Create script
        script = ai_service.create_script(request.topic)
        
        # Create video info
        video_info = {
            "id": video_id,
            "topic": request.topic,
            "script": script,
            "status": "completed",
            "created_at": time.time(),
            "duration": script["duration_estimate"],
            "style": request.style
        }
        
        # Save video info
        import json
        info_file = Path("outputs") / f"video_info_{video_id}.json"
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(video_info, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Video creation completed for ID: {video_id}")
        
    except Exception as e:
        logger.error(f"Error in video creation: {e}")
        
        # Save error info
        error_info = {
            "id": video_id,
            "status": "error",
            "error": str(e),
            "topic": request.topic,
            "created_at": time.time()
        }
        
        import json
        info_file = Path("outputs") / f"video_info_{video_id}.json"
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(error_info, f, ensure_ascii=False, indent=2)

if __name__ == "__main__":
    import uvicorn
    print("🌸 Starting Spring Automation Studio...")
    print("📖 API Documentation: http://localhost:8000/docs")
    print("🔍 Health Check: http://localhost:8000/health")
    uvicorn.run(app, host="0.0.0.0", port=8000)
