# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
from .array_json_schema_property_output import ArrayJsonSchemaPropertyOutput
from .object_json_schema_property_output import ObjectJsonSchemaPropertyOutput
import typing
import pydantic
from .llm import Llm
from .prompt_agent_output_tools_item import PromptAgentOutputToolsItem
from .knowledge_base_locator import KnowledgeBaseLocator
from .custom_llm import CustomLlm
from .rag_config import RagConfig
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class PromptAgentOutput(UncheckedBaseModel):
    prompt: typing.Optional[str] = pydantic.Field(default=None)
    """
    The prompt for the agent
    """

    llm: typing.Optional[Llm] = pydantic.Field(default=None)
    """
    The LLM to query with the prompt and the chat history
    """

    temperature: typing.Optional[float] = pydantic.Field(default=None)
    """
    The temperature for the LLM
    """

    max_tokens: typing.Optional[int] = pydantic.Field(default=None)
    """
    If greater than 0, maximum number of tokens the LLM can predict
    """

    tools: typing.Optional[typing.List[PromptAgentOutputToolsItem]] = pydantic.Field(default=None)
    """
    A list of tools that the agent can use over the course of the conversation
    """

    tool_ids: typing.Optional[typing.List[str]] = pydantic.Field(default=None)
    """
    A list of IDs of tools used by the agent
    """

    knowledge_base: typing.Optional[typing.List[KnowledgeBaseLocator]] = pydantic.Field(default=None)
    """
    A list of knowledge bases to be used by the agent
    """

    custom_llm: typing.Optional[CustomLlm] = pydantic.Field(default=None)
    """
    Definition for a custom LLM if LLM field is set to 'CUSTOM_LLM'
    """

    ignore_default_personality: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether to ignore the default personality
    """

    rag: typing.Optional[RagConfig] = pydantic.Field(default=None)
    """
    Configuration for RAG
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
