# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from .user_feedback_score import UserFeedbackScore
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic


class ConversationHistoryFeedbackCommonModel(UncheckedBaseModel):
    overall_score: typing.Optional[UserFeedbackScore] = None
    likes: typing.Optional[int] = None
    dislikes: typing.Optional[int] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
