"""
إعداد قاعدة البيانات
Database Configuration
"""

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from typing import AsyncGenerator
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

# إنشاء محرك قاعدة البيانات غير المتزامن
engine = create_async_engine(
    settings.database_url_async,
    echo=settings.DATABASE_ECHO,
    pool_pre_ping=True,
    pool_recycle=300,
    pool_size=20,
    max_overflow=0,
)

# إنشاء جلسة قاعدة البيانات غير المتزامنة
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False,
)

# إنشاء محرك قاعدة البيانات المتزامن للمهام الخاصة
sync_engine = create_engine(
    settings.DATABASE_URL,
    echo=settings.DATABASE_ECHO,
    pool_pre_ping=True,
    pool_recycle=300,
)

# إنشاء جلسة قاعدة البيانات المتزامنة
SyncSessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=sync_engine
)

# إنشاء الفئة الأساسية للنماذج
Base = declarative_base()


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    الحصول على جلسة قاعدة البيانات غير المتزامنة
    Get async database session
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception as e:
            await session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            await session.close()


def get_sync_db():
    """
    الحصول على جلسة قاعدة البيانات المتزامنة
    Get sync database session
    """
    db = SyncSessionLocal()
    try:
        yield db
        db.commit()
    except Exception as e:
        db.rollback()
        logger.error(f"Sync database session error: {e}")
        raise
    finally:
        db.close()


async def init_db():
    """
    تهيئة قاعدة البيانات
    Initialize database
    """
    try:
        async with engine.begin() as conn:
            # إنشاء جميع الجداول
            await conn.run_sync(Base.metadata.create_all)
        logger.info("✅ تم إنشاء جداول قاعدة البيانات بنجاح")
    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء جداول قاعدة البيانات: {e}")
        raise


async def close_db():
    """
    إغلاق اتصالات قاعدة البيانات
    Close database connections
    """
    try:
        await engine.dispose()
        sync_engine.dispose()
        logger.info("✅ تم إغلاق اتصالات قاعدة البيانات بنجاح")
    except Exception as e:
        logger.error(f"❌ خطأ في إغلاق اتصالات قاعدة البيانات: {e}")


class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self):
        self.engine = engine
        self.session_factory = AsyncSessionLocal
    
    async def create_session(self) -> AsyncSession:
        """إنشاء جلسة جديدة"""
        return self.session_factory()
    
    async def execute_query(self, query: str, params: dict = None):
        """تنفيذ استعلام مخصص"""
        async with self.session_factory() as session:
            try:
                result = await session.execute(query, params or {})
                await session.commit()
                return result
            except Exception as e:
                await session.rollback()
                logger.error(f"Query execution error: {e}")
                raise
    
    async def health_check(self) -> bool:
        """فحص صحة قاعدة البيانات"""
        try:
            async with self.session_factory() as session:
                await session.execute("SELECT 1")
                return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False


# إنشاء مثيل مدير قاعدة البيانات
db_manager = DatabaseManager()
