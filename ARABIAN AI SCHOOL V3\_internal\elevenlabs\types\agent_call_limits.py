# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class AgentCallLimits(UncheckedBaseModel):
    agent_concurrency_limit: typing.Optional[int] = pydantic.Field(default=None)
    """
    The maximum number of concurrent conversations. -1 indicates that there is no maximum
    """

    daily_limit: typing.Optional[int] = pydantic.Field(default=None)
    """
    The maximum number of conversations per day
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
